# 课题组网站

基于 React + Strapi + Ant Design + PostgreSQL 技术栈开发的实验小组课题组网站。

## 🚀 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 14+
- Docker & Docker Compose (可选)

### 开发环境启动（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd 课题组网站
```

2. **启动数据库**
```bash
# 只启动PostgreSQL数据库容器
docker-compose up -d postgres
```

3. **启动后端服务**
```bash
cd backend
npm install
npm run develop
```

4. **启动前端服务**
```bash
cd frontend
npm install
npm run dev
```

5. **访问应用**
- 前端: http://localhost:5173
- 后端管理: http://localhost:1337/admin

### Docker 完整部署（生产环境）

```bash
# 启动所有服务（包括前后端容器）
docker-compose up -d
```

> **注意**：当前开发流程使用npm直接启动前后端服务，只有数据库运行在Docker容器中。Docker完整部署主要用于生产环境。

## 📁 项目结构

```
课题组网站/
├── frontend/                 # React前端项目
├── backend/                  # Strapi后端项目
├── docs/                     # 项目文档
├── docker-compose.yml        # Docker部署配置
└── README.md                 # 项目说明
```

## 🛠️ 技术栈

- **后端**: Strapi v5+ (Node.js CMS框架)
- **数据库**: PostgreSQL
- **前端**: React 18+ + Vite
- **UI组件库**: Ant Design v5+
- **部署**: Docker + Docker Compose

## 📚 文档

- [功能需求文档](./docs/website-requirements.md)
- [开发进度记录](./docs/progress.md)
- [API接口文档](./docs/api-specs.md)
- [数据模型设计](./docs/schema-design.md)
- [部署指南](./docs/deployment.md)

## 🤝 贡献

请阅读 [贡献指南](./CONTRIBUTING.md) 了解如何参与项目开发。

## 📄 许可证

本项目采用 MIT 许可证。 
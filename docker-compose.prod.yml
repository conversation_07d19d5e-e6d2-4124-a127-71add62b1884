version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: research_group_postgres_prod
    environment:
      POSTGRES_DB: research_group_website
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    networks:
      - research_group_network_prod
    restart: unless-stopped

  # Strapi Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: research_group_backend_prod
    environment:
      DATABASE_CLIENT: postgres
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: research_group_website
      DATABASE_USERNAME: postgres
      DATABASE_PASSWORD: ${POSTGRES_PASSWORD:-password}
      DATABASE_SSL: false
      NODE_ENV: production
      APP_KEYS: ${APP_KEYS}
      API_TOKEN_SALT: ${API_TOKEN_SALT}
      ADMIN_JWT_SECRET: ${ADMIN_JWT_SECRET}
      JWT_SECRET: ${JWT_SECRET}
      TRANSFER_TOKEN_SALT: ${TRANSFER_TOKEN_SALT}
    ports:
      - "1337:1337"
    depends_on:
      - postgres
    networks:
      - research_group_network_prod
    restart: unless-stopped

  # React Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: research_group_frontend_prod
    environment:
      VITE_API_URL: http://localhost:1337
      NODE_ENV: production
    ports:
      - "5173:5173"
    depends_on:
      - backend
    networks:
      - research_group_network_prod
    restart: unless-stopped

volumes:
  postgres_data_prod:
    name: research_group_postgres_data_prod

networks:
  research_group_network_prod:
    driver: bridge

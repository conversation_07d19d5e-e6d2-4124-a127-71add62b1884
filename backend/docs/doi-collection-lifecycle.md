# DOI Collection 生命周期钩子使用说明

## 📋 概述

本文档说明了 `doi-collection` 内容类型的生命周期钩子功能，包括 DOI 验证和自动元数据获取功能。

## 🔧 功能特性

### beforeCreate 钩子功能

1. **DOI 格式验证**
   - 验证 DOI 是否符合标准格式（10.xxxx/xxxxxx）
   - 使用正则表达式进行严格格式检查

2. **DOI 存在性验证**
   - 通过 CrossRef API 验证 DOI 是否真实存在
   - 处理网络超时和服务不可用的情况
   - 提供详细的错误信息

3. **重复性检查**
   - 检查数据库中是否已存在相同的 DOI 记录
   - 防止重复添加相同的 DOI

### afterCreate 钩子功能

1. **元数据自动获取**
   - 从 CrossRef API 获取完整的文献元数据
   - 从 DOI 服务获取 BibTeX 格式的引文
   - 并行处理提高获取效率

2. **数据自动存储**
   - 将获取的元数据自动存储到 `paper-info` 表
   - 包含标题、作者、期刊、年份、摘要、BibTeX 等信息
   - 自动发布创建的记录

3. **错误处理**
   - 完善的错误处理和日志记录
   - 元数据获取失败不会阻止 DOI 记录创建
   - 详细的错误信息记录用于调试

## 📊 数据流程

```
用户输入 DOI
    ↓
格式验证 (beforeCreate)
    ↓
存在性验证 (beforeCreate)
    ↓
重复性检查 (beforeCreate)
    ↓
创建 DOI 记录
    ↓
获取元数据 (afterCreate)
    ↓
创建 paper-info 记录 (afterCreate)
```

## 🚀 使用方法

### 在 Strapi 管理面板中使用

1. 登录 Strapi 管理面板
2. 导航到 "DOI Collection" 内容类型
3. 点击 "Create new entry"
4. 输入有效的 DOI（例如：`10.1038/nature12373`）
5. 点击保存

### 验证结果

- **成功情况**：DOI 记录创建成功，相关元数据自动存储到 paper-info 表
- **失败情况**：显示具体的错误信息，如格式错误、DOI 不存在等

## ⚠️ 错误处理

### 常见错误类型

1. **格式错误**
   ```
   DOI 格式无效。正确格式应为：10.xxxx/xxxxxx（例如：10.1038/nature12373）
   ```

2. **DOI 不存在**
   ```
   DOI "10.xxxx/invalid" 不存在或无法访问。请检查 DOI 是否正确，或稍后重试。
   ```

3. **重复 DOI**
   ```
   DOI "10.xxxx/xxxxxx" 已存在于数据库中，不能重复添加。
   ```

4. **网络错误**
   ```
   DOI 验证超时，请检查网络连接或稍后重试
   ```

### 错误恢复

- 大部分错误会在管理面板中显示给用户
- 网络相关错误建议稍后重试
- 元数据获取失败不会影响 DOI 记录的创建

## 🔧 配置说明

### 超时设置

- DOI 验证超时：10 秒
- 元数据获取超时：15 秒

### API 端点

- CrossRef API：`https://api.crossref.org/works/{doi}`
- DOI BibTeX：`https://doi.org/{doi}`

### User-Agent

所有 API 请求都使用标识性的 User-Agent：
- 验证：`Strapi-DOI-Validator/1.0 (mailto:<EMAIL>)`
- 获取：`Strapi-DOI-Fetcher/1.0 (mailto:<EMAIL>)`

## 📝 日志记录

系统会记录以下日志信息：

- **INFO**：DOI 验证通过、元数据获取成功、记录创建成功
- **WARN**：DOI 验证失败、BibTeX 获取失败
- **ERROR**：元数据获取失败、系统错误

## 🧪 测试建议

### 测试用例

1. **有效 DOI**：`10.1038/nature12373`
2. **无效格式**：`invalid-doi`
3. **不存在的 DOI**：`10.9999/nonexistent`
4. **重复 DOI**：已存在的 DOI

### 验证步骤

1. 在管理面板中尝试创建 DOI 记录
2. 检查错误信息是否准确
3. 验证成功创建的记录是否包含完整元数据
4. 检查 paper-info 表中的相关记录

## 🔄 维护建议

1. **定期检查日志**：监控 DOI 验证和元数据获取的成功率
2. **网络监控**：确保 CrossRef API 的可访问性
3. **数据质量**：定期检查获取的元数据质量
4. **性能优化**：根据使用情况调整超时设置

## 📞 技术支持

如遇到问题，请检查：

1. 网络连接是否正常
2. CrossRef API 服务状态
3. Strapi 日志中的详细错误信息
4. DOI 格式是否正确

---

*最后更新：2025-07-28*

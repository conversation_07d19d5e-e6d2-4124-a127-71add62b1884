import axios from 'axios';
import { errors } from '@strapi/utils';
const { ApplicationError } = errors;

/**
 * DOI 验证和元数据处理中间件
 * 
 * 功能：
 * 1. 验证 DOI 格式
 * 2. 检查 DOI 是否真实存在
 * 3. 检查数据库中是否已存在相同 DOI
 * 4. 获取并存储 DOI 元数据
 */
export const doiValidationMiddleware = () => {
  return async (context: any, next: any) => {
    // 只处理 DOI Collection 的创建和更新操作
    if (context.uid !== 'api::doi-collection.doi-collection' || 
        !['create', 'update'].includes(context.action)) {
      return await next();
    }

    const { data } = context.params;
    
    // 如果没有 DOI 数据，跳过处理
    if (!data.doi) {
      return await next();
    }

    console.log('🔥🔥🔥 [DOI MIDDLEWARE] 开始处理 DOI:', data.doi);
    strapi.log.info(`🔥🔥🔥 [DOI MIDDLEWARE] 开始处理 DOI: ${data.doi}`);

    try {
      // 1. 验证 DOI 格式
      await validateDoiFormat(data.doi);
      
      // 2. 验证 DOI 是否真实存在
      await validateDoiExists(data.doi);
      
      // 3. 检查数据库中是否已存在相同 DOI
      await checkDuplicateDoi(data.doi, context);
      
      console.log('✅ [DOI MIDDLEWARE] DOI 验证通过');
      strapi.log.info('✅ [DOI MIDDLEWARE] DOI 验证通过');
      
    } catch (error: any) {
      console.error('❌ [DOI MIDDLEWARE] DOI 验证失败:', error.message);
      strapi.log.error(`❌ [DOI MIDDLEWARE] DOI 验证失败: ${error.message}`);
      throw error;
    }

    // 执行下一个中间件
    const result = await next();

    // 创建成功后，异步获取并存储元数据
    if (context.action === 'create' && result) {
      // 异步处理元数据，不阻塞响应
      setImmediate(async () => {
        try {
          await fetchAndStoreDOIMetadata(data.doi, result.documentId);
        } catch (error: any) {
          console.error('⚠️ [DOI MIDDLEWARE] 元数据获取失败:', error.message);
          strapi.log.error(`⚠️ [DOI MIDDLEWARE] 元数据获取失败: ${error.message}`);
        }
      });
    }

    return result;
  };
};

/**
 * 验证 DOI 格式
 */
async function validateDoiFormat(doi: string) {
  const doiRegex = /^10\.\d{4,}\/[^\s]+$/;
  
  if (!doiRegex.test(doi)) {
    throw new ApplicationError(
      `DOI 格式不正确。正确格式应为：10.xxxx/xxxxx，您输入的是：${doi}`,
      { details: { doi, error: 'INVALID_FORMAT' } }
    );
  }
}

/**
 * 验证 DOI 是否真实存在
 */
async function validateDoiExists(doi: string) {
  try {
    const response = await axios.get(`https://doi.org/${doi}`, {
      timeout: 10000,
      headers: {
        'Accept': 'application/json'
      }
    });
    
    if (response.status !== 200) {
      throw new ApplicationError(
        `DOI "${doi}" 不存在或无法访问`,
        { details: { doi, error: 'DOI_NOT_FOUND' } }
      );
    }
  } catch (error: any) {
    if (error.code === 'ENOTFOUND' || error.response?.status === 404) {
      throw new ApplicationError(
        `DOI "${doi}" 不存在或无法访问`,
        { details: { doi, error: 'DOI_NOT_FOUND' } }
      );
    }
    throw new ApplicationError(
      `验证 DOI "${doi}" 时发生网络错误`,
      { details: { doi, error: 'NETWORK_ERROR', originalError: error.message } }
    );
  }
}

/**
 * 检查数据库中是否已存在相同 DOI
 */
async function checkDuplicateDoi(doi: string, context: any) {
  const existingRecord = await strapi.documents('api::doi-collection.doi-collection').findFirst({
    filters: { doi }
  });

  // 如果是更新操作，排除当前记录
  if (existingRecord && context.action === 'update') {
    const currentDocumentId = context.params.documentId;
    if (existingRecord.documentId !== currentDocumentId) {
      throw new ApplicationError(
        `DOI "${doi}" 已存在于数据库中`,
        { details: { doi, error: 'DUPLICATE_DOI', existingId: existingRecord.documentId } }
      );
    }
  } else if (existingRecord && context.action === 'create') {
    throw new ApplicationError(
      `DOI "${doi}" 已存在于数据库中`,
      { details: { doi, error: 'DUPLICATE_DOI', existingId: existingRecord.documentId } }
    );
  }
}

/**
 * 获取并存储 DOI 元数据
 */
async function fetchAndStoreDOIMetadata(doi: string, documentId: string) {
  try {
    console.log('📚 [DOI MIDDLEWARE] 开始获取元数据:', doi);

    // 并行获取多个来源的元数据
    const [crossrefData, dataciteData] = await Promise.allSettled([
      fetchCrossRefMetadata(doi),
      fetchDataCiteMetadata(doi)
    ]);

    const crossrefResult = crossrefData.status === 'fulfilled' ? crossrefData.value : null;
    const dataciteResult = dataciteData.status === 'fulfilled' ? dataciteData.value : null;

    // 获取 BibTeX 格式引用
    let bibtexCitation = null;
    try {
      bibtexCitation = await fetchBibTeXCitation(doi);
    } catch (error: any) {
      console.warn('⚠️ [DOI MIDDLEWARE] BibTeX 获取失败:', error.message);
    }

    // 从 API 响应中提取并映射数据到 paper-info 表字段
    const paperInfo = extractPaperInfo(doi, crossrefResult, dataciteResult, bibtexCitation);

    console.log('📋 [DOI MIDDLEWARE] 提取的论文信息:', paperInfo);

    // 存储到 paper-info 表
    const paperInfoRecord = await strapi.documents('api::paper-info.paper-info').create({
      data: {
        ...paperInfo,
        doi_collection: documentId
      }
    });

    console.log('✅ [DOI MIDDLEWARE] 元数据存储成功');
    strapi.log.info('✅ [DOI MIDDLEWARE] 元数据存储成功');

    // 立即发布 paper-info 记录
    try {
      await strapi.documents('api::paper-info.paper-info').publish({
        documentId: paperInfoRecord.documentId
      });
      console.log('📢 [DOI MIDDLEWARE] paper-info 记录发布成功');
      strapi.log.info('📢 [DOI MIDDLEWARE] paper-info 记录发布成功');
    } catch (publishError: any) {
      console.error('⚠️ [DOI MIDDLEWARE] paper-info 记录发布失败:', publishError.message);
      strapi.log.error(`⚠️ [DOI MIDDLEWARE] paper-info 记录发布失败: ${publishError.message}`);
      // 发布失败不影响整个流程，只记录错误
    }

  } catch (error: any) {
    console.error('❌ [DOI MIDDLEWARE] 元数据处理失败:', error);
    strapi.log.error(`❌ [DOI MIDDLEWARE] 元数据处理失败: ${error.message}`);
    throw error;
  }
}

/**
 * 从 CrossRef 获取元数据
 */
async function fetchCrossRefMetadata(doi: string) {
  const response = await axios.get(`https://api.crossref.org/works/${doi}`, {
    timeout: 15000,
    headers: {
      'User-Agent': 'Strapi-DOI-Collector/1.0 (mailto:<EMAIL>)'
    }
  });
  return response.data;
}

/**
 * 从 DataCite 获取元数据
 */
async function fetchDataCiteMetadata(doi: string) {
  const response = await axios.get(`https://api.datacite.org/dois/${doi}`, {
    timeout: 15000,
    headers: {
      'Accept': 'application/json'
    }
  });
  return response.data;
}

/**
 * 获取 BibTeX 格式引用
 */
async function fetchBibTeXCitation(doi: string) {
  const response = await axios.get(`https://doi.org/${doi}`, {
    timeout: 10000,
    headers: {
      'Accept': 'application/x-bibtex'
    }
  });
  return response.data;
}

/**
 * 从 CrossRef 和 DataCite API 响应中提取论文信息并映射到 paper-info 表字段
 */
function extractPaperInfo(doi: string, crossrefData: any, dataciteData: any, bibtexCitation: string | null) {
  // 初始化返回对象，包含所有 paper-info 表的字段
  const paperInfo = {
    doi: doi,
    title: null as string | null,
    authors: null as string | null,
    year: null as number | null,
    journal: null as string | null,
    abstract: null as string | null,
    bibtex: bibtexCitation
  };

  // 从 CrossRef 数据中提取信息（优先使用 CrossRef 数据）
  if (crossrefData?.message) {
    const work = crossrefData.message;

    // 提取标题
    if (work.title && work.title.length > 0) {
      paperInfo.title = work.title[0];
    }

    // 提取作者信息
    if (work.author && Array.isArray(work.author)) {
      const authorNames = work.author.map((author: any) => {
        const given = author.given || '';
        const family = author.family || '';
        return `${given} ${family}`.trim();
      }).filter((name: string) => name.length > 0);

      if (authorNames.length > 0) {
        paperInfo.authors = authorNames.join(', ');
      }
    }

    // 提取发表年份
    if (work.published && work.published['date-parts'] && work.published['date-parts'][0]) {
      const year = work.published['date-parts'][0][0];
      if (year && typeof year === 'number') {
        paperInfo.year = year;
      }
    }

    // 提取期刊信息
    if (work['container-title'] && work['container-title'].length > 0) {
      paperInfo.journal = work['container-title'][0];
    }

    // 提取摘要
    if (work.abstract) {
      paperInfo.abstract = work.abstract;
    }
  }

  // 如果 CrossRef 数据不完整，尝试从 DataCite 数据中补充
  if (dataciteData?.data?.attributes) {
    const attrs = dataciteData.data.attributes;

    // 补充标题
    if (!paperInfo.title && attrs.titles && attrs.titles.length > 0) {
      paperInfo.title = attrs.titles[0].title;
    }

    // 补充作者信息
    if (!paperInfo.authors && attrs.creators && Array.isArray(attrs.creators)) {
      const authorNames = attrs.creators.map((creator: any) => {
        if (creator.name) {
          return creator.name;
        } else if (creator.givenName && creator.familyName) {
          return `${creator.givenName} ${creator.familyName}`;
        }
        return null;
      }).filter((name: string | null) => name !== null);

      if (authorNames.length > 0) {
        paperInfo.authors = authorNames.join(', ');
      }
    }

    // 补充发表年份
    if (!paperInfo.year && attrs.publicationYear) {
      const year = parseInt(attrs.publicationYear);
      if (!isNaN(year)) {
        paperInfo.year = year;
      }
    }

    // 补充期刊信息
    if (!paperInfo.journal && attrs.container && attrs.container.title) {
      paperInfo.journal = attrs.container.title;
    }

    // 补充摘要
    if (!paperInfo.abstract && attrs.descriptions && Array.isArray(attrs.descriptions)) {
      const abstractDesc = attrs.descriptions.find((desc: any) =>
        desc.descriptionType === 'Abstract' || desc.descriptionType === 'abstract'
      );
      if (abstractDesc && abstractDesc.description) {
        paperInfo.abstract = abstractDesc.description;
      }
    }
  }

  // 数据清理和验证
  // 确保字符串字段不会过长（根据数据库字段限制调整）
  if (paperInfo.title && paperInfo.title.length > 500) {
    paperInfo.title = paperInfo.title.substring(0, 497) + '...';
  }

  if (paperInfo.authors && paperInfo.authors.length > 1000) {
    paperInfo.authors = paperInfo.authors.substring(0, 997) + '...';
  }

  if (paperInfo.journal && paperInfo.journal.length > 200) {
    paperInfo.journal = paperInfo.journal.substring(0, 197) + '...';
  }

  if (paperInfo.abstract && paperInfo.abstract.length > 5000) {
    paperInfo.abstract = paperInfo.abstract.substring(0, 4997) + '...';
  }

  return paperInfo;
}

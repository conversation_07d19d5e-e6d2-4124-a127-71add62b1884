{"kind": "collectionType", "collectionName": "projects", "info": {"singularName": "project", "pluralName": "projects", "displayName": "project"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"funding_agency": {"type": "string", "required": true}, "project_title": {"type": "string", "required": true}, "funding_year": {"type": "integer"}, "funding_type": {"type": "string"}, "project_abs": {"type": "blocks"}, "project_pic": {"type": "media", "multiple": false, "allowedTypes": ["images"]}}}
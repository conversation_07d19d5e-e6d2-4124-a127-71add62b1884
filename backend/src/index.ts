import type { Core } from '@strapi/strapi';
import { doiValidationMiddleware } from './utils/document-service-middlewares';

export default {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register({ strapi }: { strapi: Core.Strapi }) {
    // 注册 DOI 验证和元数据处理中间件
    console.log('🚀 [STRAPI] 注册 DOI Document Service Middleware');
    strapi.documents.use(doiValidationMiddleware());
  },

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  bootstrap(/* { strapi }: { strapi: Core.Strapi } */) {},
};

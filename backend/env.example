# Database
DATABASE_CLIENT=postgres
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=research_group_website
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_SSL=false

# App Keys
APP_KEYS=your-app-keys-here
API_TOKEN_SALT=your-api-token-salt-here
ADMIN_JWT_SECRET=your-admin-jwt-secret-here
JWT_SECRET=your-jwt-secret-here

# Security
TRANSFER_TOKEN_SALT=your-transfer-token-salt-here

# File Upload
CLOUDINARY_NAME=your-cloudinary-name
CLOUDINARY_KEY=your-cloudinary-key
CLOUDINARY_SECRET=your-cloudinary-secret

# Email
SMTP_HOST=your-smtp-host
SMTP_PORT=587
SMTP_AUTH_USER=your-smtp-user
SMTP_AUTH_PASS=your-smtp-password
SMTP_DEFAULT_FROM=your-default-from-email

# Admin
ADMIN_PATH=/admin 
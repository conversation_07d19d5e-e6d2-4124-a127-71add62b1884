#!/bin/bash

# 图片显示问题修复部署脚本
# 适用于IP: *************:5000

set -e  # 遇到错误立即退出

echo "🚀 开始修复图片显示问题..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器配置
SERVER_IP="*************"
BACKEND_PORT="1337"  # Strapi后端端口
FRONTEND_PORT="5000"  # 前端服务端口

echo -e "${BLUE}📋 配置信息:${NC}"
echo -e "  服务器IP: ${SERVER_IP}"
echo -e "  Strapi后端端口: ${BACKEND_PORT}"
echo -e "  前端服务端口: ${FRONTEND_PORT}"
echo -e "  访问地址: http://${SERVER_IP}:${FRONTEND_PORT}"
echo ""

# 1. 检查环境变量配置
echo -e "${YELLOW}1. 检查环境变量配置...${NC}"
if [ -f ".env.production" ]; then
    echo "✅ .env.production 文件存在"
    echo "当前配置:"
    cat .env.production
else
    echo -e "${RED}❌ .env.production 文件不存在${NC}"
    exit 1
fi
echo ""

# 2. 重新构建前端
echo -e "${YELLOW}2. 重新构建前端项目...${NC}"
echo "清理旧的构建文件..."
rm -rf dist/

echo "开始构建..."
NODE_ENV=production npm run build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 前端构建成功${NC}"
else
    echo -e "${RED}❌ 前端构建失败${NC}"
    exit 1
fi
echo ""

# 3. 验证构建结果中的API URL
echo -e "${YELLOW}3. 验证构建结果中的API配置...${NC}"
if grep -r "${SERVER_IP}:${FRONTEND_PORT}" dist/ > /dev/null; then
    echo -e "${GREEN}✅ 构建文件中包含正确的API URL${NC}"
    echo "找到的API URL:"
    grep -r "${SERVER_IP}:${FRONTEND_PORT}" dist/ | head -3

    # 检查是否有重复的/api路径
    if grep -r "/api/api/" dist/ > /dev/null; then
        echo -e "${RED}❌ 发现重复的API路径！${NC}"
        echo "请检查环境变量配置，VITE_API_URL不应包含/api后缀"
        exit 1
    else
        echo -e "${GREEN}✅ 没有发现重复的API路径${NC}"
    fi
else
    echo -e "${RED}❌ 构建文件中未找到正确的API URL${NC}"
    echo "请检查 .env.production 配置"
    exit 1
fi
echo ""

# 4. 检查nginx配置文件
echo -e "${YELLOW}4. 检查nginx配置文件...${NC}"
if [ -f "nginx-ip.conf" ]; then
    echo "✅ nginx-ip.conf 文件存在"
    
    # 检查关键配置
    if grep -q "${SERVER_IP}" nginx-ip.conf; then
        echo -e "${GREEN}✅ nginx配置包含正确的服务器IP${NC}"
    else
        echo -e "${RED}❌ nginx配置中未找到正确的服务器IP${NC}"
        exit 1
    fi
    
    if grep -q "127.0.0.1:${BACKEND_PORT}" nginx-ip.conf; then
        echo -e "${GREEN}✅ nginx配置包含正确的后端端口${NC}"
    else
        echo -e "${RED}❌ nginx配置中未找到正确的后端端口${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ nginx-ip.conf 文件不存在${NC}"
    exit 1
fi
echo ""

# 5. 显示部署命令
echo -e "${YELLOW}5. 服务器部署命令:${NC}"
echo ""
echo -e "${BLUE}在服务器上执行以下命令:${NC}"
echo ""

echo -e "${GREEN}# 1. 上传构建文件${NC}"
echo "scp -r dist/ user@${SERVER_IP}:/var/www/geocues-lab/"
echo ""

echo -e "${GREEN}# 2. 更新nginx配置${NC}"
echo "scp nginx-ip.conf user@${SERVER_IP}:/tmp/"
echo "sudo cp /tmp/nginx-ip.conf /etc/nginx/sites-available/geocues-lab"
echo "sudo ln -sf /etc/nginx/sites-available/geocues-lab /etc/nginx/sites-enabled/"
echo ""

echo -e "${GREEN}# 3. 测试并重启nginx${NC}"
echo "sudo nginx -t"
echo "sudo systemctl restart nginx"
echo ""

echo -e "${GREEN}# 4. 重启Strapi服务 (使用默认端口${BACKEND_PORT})${NC}"
echo "cd /path/to/backend"
echo "NODE_ENV=production pm2 restart strapi"
echo "# 或者"
echo "NODE_ENV=production npm start"
echo ""

# 6. 测试命令
echo -e "${YELLOW}6. 部署后测试命令:${NC}"
echo ""
echo -e "${GREEN}# 测试API连接${NC}"
echo "curl http://${SERVER_IP}:${FRONTEND_PORT}/api/projects"
echo ""

echo -e "${GREEN}# 测试图片访问 (替换为实际图片路径)${NC}"
echo "curl -I http://${SERVER_IP}:${FRONTEND_PORT}/api/uploads/small_image_example.jpg"
echo ""

echo -e "${GREEN}# 检查nginx状态${NC}"
echo "sudo systemctl status nginx"
echo ""

echo -e "${GREEN}# 检查端口占用${NC}"
echo "sudo netstat -tlnp | grep :${BACKEND_PORT}"
echo ""

# 7. 故障排查
echo -e "${YELLOW}7. 故障排查命令:${NC}"
echo ""
echo -e "${GREEN}# 查看nginx错误日志${NC}"
echo "sudo tail -f /var/log/nginx/error.log"
echo ""

echo -e "${GREEN}# 查看nginx访问日志${NC}"
echo "sudo tail -f /var/log/nginx/geocues-lab-ip.access.log"
echo ""

echo -e "${GREEN}# 检查Strapi日志${NC}"
echo "pm2 logs strapi"
echo ""

echo -e "${BLUE}🎉 修复脚本执行完成！${NC}"
echo ""
echo -e "${YELLOW}📝 接下来的步骤:${NC}"
echo "1. 将构建文件上传到服务器"
echo "2. 更新nginx配置"
echo "3. 重启相关服务"
echo "4. 测试图片显示功能"
echo ""
echo -e "${BLUE}📖 详细说明请参考: IMAGE_DISPLAY_SOLUTION.md${NC}"

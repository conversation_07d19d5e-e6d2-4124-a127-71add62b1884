# IP地址部署指南

当只有服务器IP而没有域名时的完整部署配置。

## 🎯 配置概览

### 服务器信息
- **IP地址**: `*************`
- **前端端口**: `5000` (通过Nginx)
- **后端端口**: `1337` (仅内网访问)
- **API访问**: `http://*************:5000/api/*`

## 📋 配置步骤

### 1. 前端环境变量配置

**开发环境** (`.env`):
```bash
VITE_API_URL=http://localhost:1337
```

**生产环境** (`.env.production`):
```bash
VITE_API_URL=http://*************:5000/api
```

### 2. 后端安全配置

**Strapi配置** (`backend/config/server.ts`):
```typescript
export default ({ env }) => ({
  // 生产环境只监听内网，开发环境允许外部访问
  host: env('HOST', process.env.NODE_ENV === 'production' ? '127.0.0.1' : '0.0.0.0'),
  port: env.int('PORT', 1337),  // Strapi保持1337端口
  app: {
    keys: env.array('APP_KEYS'),
  },
});
```

### 3. Nginx配置要点

**关键配置** (`nginx-ip.conf`):
```nginx
server {
    listen 80;
    server_name *************;  # 使用IP地址
    root /var/www/geocues-lab/dist;
    
    # API代理 - 核心配置
    location /api/ {
        proxy_pass http://127.0.0.1:1337/;  # 代理到内网Strapi
        # ... 其他代理配置
    }
    
    # SPA路由处理
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 🚀 部署流程

### 自动部署
```bash
# 使用提供的部署脚本
./deploy-ip.sh
```

### 手动部署

**1. 构建前端**:
```bash
npm run build
```

**2. 上传文件**:
```bash
# 上传构建文件
rsync -avz dist/ root@*************:/var/www/geocues-lab/dist/

# 上传Nginx配置
scp nginx-ip.conf root@*************:/etc/nginx/sites-available/geocues-lab-ip
```

**3. 配置Nginx**:
```bash
# SSH到服务器
ssh root@*************

# 启用配置
ln -sf /etc/nginx/sites-available/geocues-lab-ip /etc/nginx/sites-enabled/

# 测试配置
nginx -t

# 重启Nginx
systemctl reload nginx
```

**4. 启动后端**:
```bash
# 在后端目录
cd /path/to/backend
NODE_ENV=production npm start
```

## 🔧 API代理工作原理

### 请求流程
```
用户浏览器
    ↓ 请求: http://*************/api/papers
Nginx (80端口)
    ↓ 代理转发: http://127.0.0.1:1337/papers
Strapi后端 (1337端口，仅内网)
    ↓ 响应数据
Nginx
    ↓ 返回给用户
用户浏览器
```

### 前端代码示例
```javascript
// 开发环境
const API_BASE = 'http://localhost:1337';
fetch(`${API_BASE}/api/papers`);

// 生产环境（通过代理）
const API_BASE = '/api';  // 相对路径，自动使用当前域名
fetch(`${API_BASE}/papers`);  // 实际请求: http://*************/api/papers
```

## 🔒 安全优势

### 使用API代理的好处:
1. **隐藏后端端口**: 外网无法直接访问1337端口
2. **统一入口**: 所有请求都通过80端口
3. **简化SSL**: 只需为80/443端口配置证书
4. **防火墙友好**: 只需开放80/443端口

### 网络安全配置:
```bash
# 防火墙规则示例
ufw allow 80/tcp    # 允许HTTP
ufw allow 443/tcp   # 允许HTTPS（如果配置SSL）
ufw allow 22/tcp    # 允许SSH
ufw deny 1337/tcp   # 拒绝直接访问后端端口
```

## 🧪 测试验证

### 1. 前端路由测试
```bash
# 测试SPA路由
curl -I http://*************/contact
curl -I http://*************/team
curl -I http://*************/research

# 应该返回200状态码
```

### 2. API代理测试
```bash
# 测试API代理
curl http://*************/api/papers
curl http://*************/api/team-members

# 应该返回JSON数据
```

### 3. 静态资源测试
```bash
# 测试静态资源缓存
curl -I http://*************/assets/index-xxx.js

# 应该包含缓存头: Cache-Control: public, immutable
```

## 🐛 常见问题

### Q: API请求失败，返回404
**A**: 检查Nginx配置中的proxy_pass路径
```nginx
# ❌ 错误
proxy_pass http://127.0.0.1:1337;

# ✅ 正确
proxy_pass http://127.0.0.1:1337/;
```

### Q: 直接访问路由仍然404
**A**: 检查try_files配置和文件权限
```bash
# 检查文件权限
ls -la /var/www/geocues-lab/dist/
chown -R www-data:www-data /var/www/geocues-lab/dist/
```

### Q: CORS错误
**A**: 检查Nginx CORS头配置
```nginx
add_header Access-Control-Allow-Origin "http://*************";
```

## 📈 性能优化

### 1. 启用压缩
```nginx
gzip on;
gzip_types text/css application/javascript;
```

### 2. 设置缓存
```nginx
# 静态资源长期缓存
location ~* \.(js|css|png|jpg)$ {
    expires 1y;
}

# HTML不缓存
location ~* \.html$ {
    add_header Cache-Control "no-cache";
}
```

### 3. 监控日志
```bash
# 查看访问日志
tail -f /var/log/nginx/geocues-lab-ip.access.log

# 查看错误日志
tail -f /var/log/nginx/geocues-lab-ip.error.log
```

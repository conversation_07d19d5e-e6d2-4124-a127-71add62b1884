# Nginx 配置详解

## 🎯 两个关键 location 块的作用

### 1. 静态资源处理 location

```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    try_files $uri =404;
}
```

**详细解释：**
- `~*` = 不区分大小写的正则匹配
- `\.(js|css|...)$` = 匹配以这些扩展名结尾的文件
- `expires 1y` = 设置1年过期时间
- `immutable` = 告诉浏览器文件永不改变
- `try_files $uri =404` = 文件不存在直接404，不走SPA路由

**举例说明：**
```
请求: https://geocues-lab.com/assets/index-abc123.js
结果: ✅ 匹配此规则，设置长期缓存，直接返回文件

请求: https://geocues-lab.com/assets/logo.png  
结果: ✅ 匹配此规则，设置长期缓存，直接返回图片

请求: https://geocues-lab.com/contact
结果: ❌ 不匹配此规则，继续到下一个location
```

### 2. SPA 路由处理 location

```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

**详细解释：**
- `location /` = 匹配所有请求（优先级最低）
- `try_files` 按顺序尝试：
  1. `$uri` = 尝试访问请求的文件
  2. `$uri/` = 尝试作为目录访问
  3. `/index.html` = 最后回退到index.html

**举例说明：**
```
请求: https://geocues-lab.com/contact
1. 尝试 /var/www/dist/contact 文件 ❌ 不存在
2. 尝试 /var/www/dist/contact/ 目录 ❌ 不存在  
3. 返回 /var/www/dist/index.html ✅ React Router处理

请求: https://geocues-lab.com/team/member/123
1. 尝试 /var/www/dist/team/member/123 ❌ 不存在
2. 尝试 /var/www/dist/team/member/123/ ❌ 不存在
3. 返回 /var/www/dist/index.html ✅ React Router处理
```

## 🔧 生产环境配置步骤

### 步骤1: 修改域名和路径

```nginx
server_name your-actual-domain.com;  # 改为你的域名
root /var/www/your-project/dist;     # 改为你的实际路径
```

### 步骤2: 配置后端API代理

**情况A: 后端在同一服务器**
```nginx
location /api/ {
    proxy_pass http://127.0.0.1:1337/;  # 本地端口
}
```

**情况B: 后端在不同服务器**
```nginx
location /api/ {
    proxy_pass http://api.your-domain.com/;  # 后端域名
}
```

**情况C: 使用负载均衡**
```nginx
upstream backend {
    server *********:1337;
    server *********:1337;
}

location /api/ {
    proxy_pass http://backend/;
}
```

### 步骤3: 部署到服务器

```bash
# 1. 复制配置文件
sudo cp nginx.conf /etc/nginx/sites-available/geocues-lab

# 2. 创建软链接
sudo ln -s /etc/nginx/sites-available/geocues-lab /etc/nginx/sites-enabled/

# 3. 测试配置
sudo nginx -t

# 4. 重启nginx
sudo systemctl restart nginx
```

## 🚀 性能优化配置

### 启用HTTP/2和压缩
```nginx
listen 443 ssl http2;  # 启用HTTP/2
gzip on;               # 启用压缩
gzip_types text/css application/javascript;
```

### 优化缓存策略
```nginx
# HTML文件不缓存
location ~* \.html$ {
    add_header Cache-Control "no-cache";
}

# 静态资源长期缓存
location ~* \.(js|css|png|jpg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 🔒 安全配置

### SSL/HTTPS配置
```nginx
ssl_certificate /path/to/cert.pem;
ssl_certificate_key /path/to/key.pem;
ssl_protocols TLSv1.2 TLSv1.3;
```

### 安全响应头
```nginx
add_header X-Frame-Options "SAMEORIGIN";
add_header X-Content-Type-Options "nosniff";
add_header X-XSS-Protection "1; mode=block";
```

## 🐛 常见问题排查

### 问题1: 直接访问路由仍然404
**检查：** location顺序是否正确
```nginx
# ✅ 正确顺序：先匹配静态资源，再匹配SPA路由
location ~* \.(js|css)$ { ... }  # 具体匹配
location / { ... }               # 通用匹配
```

### 问题2: API请求失败
**检查：** proxy_pass配置
```nginx
# ❌ 错误：多了斜杠
proxy_pass http://localhost:1337//;

# ✅ 正确
proxy_pass http://localhost:1337/;
```

### 问题3: 静态资源缓存不生效
**检查：** 文件扩展名匹配
```nginx
# 确保包含所有需要缓存的文件类型
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
```

# Nginx 配置文件 - 用于生产环境部署
# 将此文件放在 /etc/nginx/sites-available/ 目录下

# 示例1: 基础配置
server {
    listen 80;
    server_name geocues-lab.com www.geocues-lab.com;  # 替换为你的实际域名
    root /var/www/geocues-lab/dist;  # 替换为你的构建文件绝对路径
    index index.html;

    # 访问日志
    access_log /var/log/nginx/geocues-lab.access.log;
    error_log /var/log/nginx/geocues-lab.error.log;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # SPA 路由回退配置 - 关键配置
    location / {
        try_files $uri $uri/ /index.html;
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # API 代理配置 - 根据你的后端部署情况选择
    location /api/ {
        # 选项1: 后端在同一服务器的不同端口
        proxy_pass http://127.0.0.1:1337/;

        # 选项2: 后端在不同服务器
        # proxy_pass http://backend.geocues-lab.com/;

        # 选项3: 使用upstream负载均衡
        # proxy_pass http://backend_servers/;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 错误页面处理
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# 示例2: HTTPS + SSL 配置
server {
    listen 443 ssl http2;
    server_name geocues-lab.com www.geocues-lab.com;
    root /var/www/geocues-lab/dist;
    index index.html;

    # SSL 证书配置
    ssl_certificate /etc/ssl/certs/geocues-lab.com.crt;
    ssl_certificate_key /etc/ssl/private/geocues-lab.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 静态资源缓存 - 生产环境优化
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        try_files $uri =404;

        # 跨域设置（如果需要）
        # add_header Access-Control-Allow-Origin "https://geocues-lab.com";
    }

    # SPA 路由回退配置
    location / {
        try_files $uri $uri/ /index.html;

        # HTML 文件不缓存，确保更新及时
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
    }

    # API 代理 - HTTPS 版本
    location /api/ {
        proxy_pass https://api.geocues-lab.com/;  # 使用HTTPS后端
        proxy_ssl_verify off;  # 如果使用自签名证书
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name geocues-lab.com www.geocues-lab.com;
    return 301 https://$server_name$request_uri;
}

# upstream 负载均衡配置示例（如果有多个后端服务器）
upstream backend_servers {
    server 127.0.0.1:1337 weight=3;
    server 127.0.0.1:1338 weight=2;
    server 127.0.0.1:1339 weight=1 backup;
}

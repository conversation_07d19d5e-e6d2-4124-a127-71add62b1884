docker#!/bin/bash

echo "=== API 连接测试 ==="
echo "测试时间: $(date)"
echo ""

# 测试基础连接
echo "1. 测试基础连接"
echo "测试 Strapi 直接连接 (端口 1337):"
curl -s -o /dev/null -w "HTTP状态: %{http_code}, 响应时间: %{time_total}s\n" "http://106.13.45.251:1337/api/projects?pagination[pageSize]=1" || echo "连接失败"

echo ""
echo "测试前端代理连接 (端口 5000):"
curl -s -o /dev/null -w "HTTP状态: %{http_code}, 响应时间: %{time_total}s\n" "http://106.13.45.251:5000/api/projects?pagination[pageSize]=1" || echo "连接失败"

echo ""
echo "2. 测试具体API端点"

# 测试各个API端点
endpoints=(
    "projects"
    "team-members" 
    "research-topics"
    "news"
    "contact-us"
    "paper-infos"
)

for endpoint in "${endpoints[@]}"; do
    echo ""
    echo "测试 /${endpoint}:"
    
    # 直接连接测试
    echo "  直接连接 (1337):"
    response=$(curl -s "http://106.13.45.251:1337/api/${endpoint}?pagination[pageSize]=1")
    if [ $? -eq 0 ]; then
        echo "    ✓ 连接成功"
        echo "    数据: $(echo "$response" | jq -r '.data | length // "无数据"' 2>/dev/null || echo "解析失败")"
    else
        echo "    ✗ 连接失败"
    fi
    
    # 代理连接测试
    echo "  代理连接 (5000):"
    response=$(curl -s "http://106.13.45.251:5000/api/${endpoint}?pagination[pageSize]=1")
    if [ $? -eq 0 ]; then
        echo "    ✓ 连接成功"
        echo "    数据: $(echo "$response" | jq -r '.data | length // "无数据"' 2>/dev/null || echo "解析失败")"
        
        # 检查是否返回了错误信息
        error_msg=$(echo "$response" | jq -r '.error.message // empty' 2>/dev/null)
        if [ -n "$error_msg" ]; then
            echo "    ⚠ API错误: $error_msg"
        fi
    else
        echo "    ✗ 连接失败"
    fi
done

echo ""
echo "3. 测试图片代理"
echo "测试图片URL (如果有的话):"

# 获取一个项目的图片URL
project_response=$(curl -s "http://106.13.45.251:1337/api/projects?populate=project_pic&pagination[pageSize]=1")
image_url=$(echo "$project_response" | jq -r '.data[0].project_pic.url // empty' 2>/dev/null)

if [ -n "$image_url" ]; then
    echo "找到图片URL: $image_url"
    
    echo "  直接访问 (1337):"
    curl -s -o /dev/null -w "HTTP状态: %{http_code}\n" "http://106.13.45.251:1337${image_url}" || echo "连接失败"
    
    echo "  代理访问 (5000):"
    curl -s -o /dev/null -w "HTTP状态: %{http_code}\n" "http://106.13.45.251:5000${image_url}" || echo "连接失败"
else
    echo "未找到图片URL进行测试"
fi

echo ""
echo "4. 检查nginx配置"
echo "检查nginx是否在运行:"
if command -v nginx >/dev/null 2>&1; then
    nginx -t 2>&1 | head -5
else
    echo "nginx命令不可用，无法检查配置"
fi

echo ""
echo "=== 测试完成 ==="

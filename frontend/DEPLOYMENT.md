# 部署指南

本文档说明如何部署 React SPA 应用并解决路由访问问题。

## 🚀 构建优化

### 1. 代码分割效果
经过配置后，构建将生成多个较小的文件：
- `react-vendor.js` - React 相关库
- `antd-vendor.js` - Ant Design 组件库
- `three-vendor.js` - Three.js 3D 库
- `utils-vendor.js` - 工具库
- `index.js` - 应用主代码

### 2. 懒加载效果
- 首页加载更快
- 按需加载其他页面
- 改善用户体验

## 🛠️ 服务器配置

### Nginx 部署

1. 将构建文件复制到服务器：
```bash
npm run build
scp -r dist/* user@server:/var/www/html/
```

2. 使用提供的 `nginx.conf` 配置文件
3. 重启 Nginx：
```bash
sudo systemctl restart nginx
```

### Apache 部署

1. 将构建文件复制到服务器
2. 将 `apache.conf` 内容复制到 `.htaccess` 文件：
```bash
cp apache.conf dist/.htaccess
```

### Netlify 部署

1. 将 `_redirects` 文件复制到 `public` 目录
2. 推送到 Git 仓库
3. 在 Netlify 中连接仓库

### Vercel 部署

1. 确保 `vercel.json` 在项目根目录
2. 使用 Vercel CLI 或 GitHub 集成部署

## 🔧 本地测试

### 开发环境
```bash
npm run dev
# 现在支持直接访问 http://localhost:5173/contact
```

### 生产环境预览
```bash
npm run build
npm run preview
# 现在支持直接访问 http://localhost:4173/contact
```

## ⚡ 性能优化建议

1. **启用 CDN**：将静态资源托管到 CDN
2. **启用 HTTP/2**：提升并行加载性能
3. **预加载关键资源**：在 `index.html` 中添加 `<link rel="preload">`
4. **Service Worker**：考虑添加 PWA 支持

## 🐛 常见问题

### Q: 直接访问路由仍然 404？
A: 检查服务器配置是否正确应用，确保重启了服务器。

### Q: 构建文件仍然很大？
A: 检查是否有未使用的依赖，考虑使用 webpack-bundle-analyzer 分析。

### Q: 懒加载导致白屏？
A: 检查网络连接，考虑添加更好的加载状态。

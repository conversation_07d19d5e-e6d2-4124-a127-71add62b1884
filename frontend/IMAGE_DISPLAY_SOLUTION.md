# 图片显示问题解决方案

## 🔍 问题分析

### 当前配置
- **前端访问地址**: `*************:5000`
- **Strapi后端端口**: `1337` (内网)
- **前端API配置**: `http://*************:5000/api`

### 问题根源
1. **Nginx端口配置**：需要在5000端口提供前端服务
2. **API代理配置**：nginx需要将/api/*请求代理到1337端口的Strapi
3. **CORS配置**：需要正确配置跨域头信息

## 🛠️ 解决方案

### 1. 更新Nginx配置

**文件**: `frontend/nginx-ip.conf`

关键修改点：
```nginx
server {
    listen 5000;  # ✅ 前端服务在5000端口
    server_name *************;  # ✅ 更新为真实IP

    # API代理配置
    location /api/ {
        proxy_pass http://127.0.0.1:1337/;  # ✅ 代理到Strapi的1337端口

        # CORS配置
        add_header Access-Control-Allow-Origin "http://*************:5000";
        # ... 其他配置
    }
}
```

### 2. 前端图片URL处理逻辑

**当前实现** (已正确):
```typescript
// 在各个页面组件中
const getImageUrl = (item: any): string | undefined => {
  if (!item.image_field) return undefined;
  
  const image = item.image_field;
  const url = image.formats?.small?.url || image.url;
  
  // 关键：如果是相对路径，添加API基础URL
  if (url && url.startsWith('/')) {
    return `${import.meta.env.VITE_API_URL}${url}`;
  }
  
  return url;
};
```

### 3. 环境变量配置

**生产环境** (`.env.production`):
```bash
VITE_API_URL=http://*************:5000/api
```

### 4. 后端端口配置

**文件**: `backend/config/server.ts`
```typescript
export default ({ env }) => ({
  host: env('HOST', process.env.NODE_ENV === 'production' ? '127.0.0.1' : '0.0.0.0'),
  port: env.int('PORT', 1337),  # ✅ Strapi保持1337端口
  app: {
    keys: env.array('APP_KEYS'),
  },
});
```

## 🔄 部署流程

### 1. 重新构建前端
```bash
cd frontend
NODE_ENV=production npm run build
```

### 2. 更新Nginx配置
```bash
# 复制更新后的配置文件
sudo cp nginx-ip.conf /etc/nginx/sites-available/geocues-lab
sudo ln -sf /etc/nginx/sites-available/geocues-lab /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 3. 重启Strapi服务
```bash
cd backend
# Strapi使用默认1337端口
NODE_ENV=production npm start
```

## 🧪 测试验证

### 1. 检查API连接
```bash
# 测试API是否可访问
curl http://*************:5000/api/projects

# 检查图片资源
curl -I http://*************:5000/api/uploads/small_image_123.jpg
```

### 2. 浏览器测试
1. 访问 `http://*************:5000`
2. 检查开发者工具Network面板
3. 确认图片请求URL格式正确
4. 验证图片能正常加载

### 3. 图片URL格式验证
正确的图片URL应该是：
```
http://*************:5000/api/uploads/small_image_abc123.jpg
```

## 🚨 常见问题排查

### 问题1：图片404错误
**原因**: nginx代理配置错误
**解决**: 检查nginx配置中的proxy_pass是否指向正确端口

### 问题2：CORS错误
**原因**: nginx CORS头配置不正确
**解决**: 确保Access-Control-Allow-Origin使用正确的IP地址

### 问题3：图片URL拼接错误
**原因**: 环境变量配置不正确
**解决**: 检查.env.production文件中的VITE_API_URL配置

## 📝 配置检查清单

- [ ] nginx-ip.conf中server_name使用*************
- [ ] nginx-ip.conf中proxy_pass指向127.0.0.1:5000
- [ ] .env.production中VITE_API_URL正确配置
- [ ] backend/config/server.ts中端口设置为5000
- [ ] 前端重新构建并部署
- [ ] nginx配置重新加载
- [ ] Strapi服务重启
- [ ] 图片URL测试通过

## 🔧 调试命令

```bash
# 检查nginx配置
sudo nginx -t

# 查看nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 检查端口占用
sudo netstat -tlnp | grep :5000

# 测试API连接
curl -v http://*************/api/projects

# 检查图片访问
curl -I http://*************/api/uploads/[图片文件名]
```

import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            // 将 React 相关库分离
            'react-vendor': ['react', 'react-dom', 'react-router-dom'],
            // 将 Ant Design 分离
            'antd-vendor': ['antd', '@ant-design/icons'],
            // 将 Three.js 相关库分离
            'three-vendor': ['three', '@react-three/fiber', '@react-three/drei'],
            // 将其他第三方库分离
            'utils-vendor': ['axios', 'leaflet'],
          },
        },
      },
      // 设置 chunk 大小警告限制
      chunkSizeWarningLimit: 1000,
    },
    server: {
      port: 5173,
      host: true,
      // 配置 SPA 路由回退
      historyApiFallback: true,
      proxy: {
        '/api': {
          target: env.VITE_API_URL || 'http://localhost:1337',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    // 预览服务器配置（用于 npm run preview）
    preview: {
      port: 4173,
      host: true,
      // 配置 SPA 路由回退
      historyApiFallback: true,
    },
  }
})

import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider, theme, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { ThemeProvider, useTheme } from './contexts/theme';
import Layout from './components/Layout';

// 懒加载页面组件
const Home = React.lazy(() => import('./pages/Home'));
const Team = React.lazy(() => import('./pages/Team'));
const Research = React.lazy(() => import('./pages/Research'));
const Projects = React.lazy(() => import('./pages/Projects'));
const ProjectDetail = React.lazy(() => import('./pages/Projects/ProjectDetail'));
const Publications = React.lazy(() => import('./pages/Publications'));
const News = React.lazy(() => import('./pages/News'));
const NewsDetail = React.lazy(() => import('./pages/News/NewsDetail'));
const Contact = React.lazy(() => import('./pages/Contact'));


import './styles/global.css';

// 内部应用组件，用于访问主题上下文
const AppContent: React.FC = () => {
  const { theme: currentTheme } = useTheme();

  // Ant Design 主题配置
  const antdThemeConfig = {
    algorithm: currentTheme.mode === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      colorPrimary: currentTheme.colors.primary,
      colorSuccess: currentTheme.colors.success,
      colorWarning: currentTheme.colors.warning,
      colorError: currentTheme.colors.error,
      colorInfo: currentTheme.colors.info,
      colorBgBase: currentTheme.colors.background.secondary,
      colorBgContainer: currentTheme.colors.background.card,
      colorBgElevated: currentTheme.colors.background.elevated,
      colorText: currentTheme.colors.text.primary,
      colorTextSecondary: currentTheme.colors.text.secondary,
      colorTextTertiary: currentTheme.colors.text.tertiary,
      colorTextDisabled: currentTheme.colors.text.disabled,
      colorBorder: currentTheme.colors.border.primary,
      colorBorderSecondary: currentTheme.colors.border.secondary,
      borderRadius: 8,
      boxShadow: currentTheme.colors.shadow.sm,
      boxShadowSecondary: currentTheme.colors.shadow.md,
    },
    components: {
      Layout: {
        headerBg: currentTheme.colors.background.secondary,
        footerBg: currentTheme.colors.background.tertiary,
        bodyBg: currentTheme.colors.background.primary,
      },
      Menu: {
        itemBg: 'transparent',
        itemSelectedBg: currentTheme.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 122, 255, 0.1)',
        itemHoverBg: currentTheme.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 122, 255, 0.05)',
      },
      Card: {
        colorBgContainer: currentTheme.colors.background.card,
        colorBorderSecondary: currentTheme.colors.border.secondary,
      },
      Button: {
        colorPrimary: currentTheme.colors.primary,
        colorPrimaryHover: currentTheme.colors.primary,
        colorPrimaryActive: currentTheme.colors.primary,
      },
    },
  };

  return (
    <ConfigProvider
      locale={zhCN}
      theme={antdThemeConfig}
    >
      <Router>
        <Layout>
          <Suspense fallback={
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '60vh'
            }}>
              <Spin size="large" />
            </div>
          }>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/team" element={<Team />} />
              <Route path="/research" element={<Research />} />
              <Route path="/projects" element={<Projects />} />
              <Route path="/projects/:id" element={<ProjectDetail />} />
              <Route path="/publications" element={<Publications />} />
              <Route path="/news" element={<News />} />
              <Route path="/news/:id" element={<NewsDetail />} />
              <Route path="/contact" element={<Contact />} />
            </Routes>
          </Suspense>
        </Layout>
      </Router>
    </ConfigProvider>
  );
};

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
};

export default App;

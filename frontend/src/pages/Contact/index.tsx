import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Typography, Spin, message } from 'antd';
import { EnvironmentOutlined, MailOutlined, LoadingOutlined } from '@ant-design/icons';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import ContactService from '../../api/contact';
import type { ContactInfo } from '../../types';

const { Title, Paragraph } = Typography;

// 修复 Leaflet 默认图标问题
delete (L.Icon.Default.prototype as unknown as Record<string, unknown>)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const Contact: React.FC = () => {
  // 状态管理
  const [contactInfo, setContactInfo] = useState<ContactInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [currentTheme, setCurrentTheme] = useState<string>('dark');
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);

  // 监听主题变化
  useEffect(() => {
    const updateTheme = () => {
      const theme = document.documentElement.getAttribute('data-theme') || 'dark';
      setCurrentTheme(theme);
    };

    // 初始化主题
    updateTheme();

    // 监听主题变化
    const observer = new MutationObserver(updateTheme);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  // 获取联系信息数据
  useEffect(() => {
    const fetchContactInfo = async () => {
      try {
        setLoading(true);
        setError(null);

        const data = await ContactService.getContactInfo();
        setContactInfo(data);
      } catch (error) {
        console.error('获取联系信息失败:', error);
        setError('获取联系信息失败，请稍后重试');
        message.error('获取联系信息失败');
      } finally {
        setLoading(false);
      }
    };

    fetchContactInfo();
  }, []);

  // 初始化地图
  useEffect(() => {
    if (!contactInfo || !mapRef.current) {
      return;
    }

    // 如果地图已存在，先清理
    if (mapInstanceRef.current) {
      mapInstanceRef.current.remove();
      mapInstanceRef.current = null;
    }

    try {
      // 创建地图实例
      const map = L.map(mapRef.current).setView([contactInfo.lat, contactInfo.lng], 15);
      mapInstanceRef.current = map;

      // 检查当前主题模式
      const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

      // 根据主题选择地图瓦片层
      const tileLayer = isDarkMode
        ? L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors, © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
          })
        : L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
          });

      tileLayer.addTo(map);

      // 获取当前主题的颜色值
      const computedStyle = getComputedStyle(document.documentElement);
      const bgPrimary = computedStyle.getPropertyValue('--bg-primary').trim() || (isDarkMode ? '#1f1f1f' : '#ffffff');
      const textPrimary = computedStyle.getPropertyValue('--text-primary').trim() || (isDarkMode ? '#ffffff' : '#000000');
      const textSecondary = computedStyle.getPropertyValue('--text-secondary').trim() || (isDarkMode ? '#a6a6a6' : '#666666');
      const colorPrimary = computedStyle.getPropertyValue('--color-primary').trim() || '#1890ff';
      const borderPrimary = computedStyle.getPropertyValue('--border-primary').trim() || (isDarkMode ? '#434343' : '#d9d9d9');

      // 添加自定义CSS样式来覆盖Leaflet默认popup样式
      const addCustomPopupStyles = () => {
        const styleId = 'leaflet-popup-custom-styles';
        let existingStyle = document.getElementById(styleId);

        if (existingStyle) {
          existingStyle.remove();
        }

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
          .leaflet-popup-content-wrapper {
            background: ${bgPrimary} !important;
            color: ${textPrimary} !important;
            border: 1px solid ${borderPrimary} !important;
            border-radius: 8px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
          }
          .leaflet-popup-tip {
            background: ${bgPrimary} !important;
            border: 1px solid ${borderPrimary} !important;
            border-top: none !important;
            border-right: none !important;
          }
          .leaflet-popup-close-button {
            color: ${textSecondary} !important;
            font-size: 18px !important;
            padding: 4px 8px !important;
          }
          .leaflet-popup-close-button:hover {
            color: ${textPrimary} !important;
            background: transparent !important;
          }
        `;
        document.head.appendChild(style);
      };

      // 应用自定义样式
      addCustomPopupStyles();

      // 创建自定义标记图标
      const customIcon = L.divIcon({
        html: `
          <div style="
            background: ${colorPrimary};
            width: 24px;
            height: 24px;
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            border: 3px solid ${bgPrimary};
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            <div style="
              transform: rotate(45deg);
              color: white;
              font-size: 12px;
              font-weight: bold;
            ">📍</div>
          </div>
        `,
        className: 'custom-marker',
        iconSize: [24, 24],
        iconAnchor: [12, 24],
        popupAnchor: [0, -24]
      });

      // 添加标记点
      L.marker([contactInfo.lat, contactInfo.lng], { icon: customIcon })
        .addTo(map)
        .bindPopup(`
          <div style="
            text-align: center;
            padding: 12px;
            background: ${bgPrimary};
            color: ${textPrimary};
            border-radius: 8px;
            border: 1px solid ${borderPrimary};
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          ">
            <strong style="color: ${colorPrimary}; font-size: 16px;">GeoCUES Lab</strong><br/>
            <span style="color: ${textSecondary}; font-size: 14px; margin-top: 4px; display: block;">
              ${contactInfo.address}
            </span>
          </div>
        `)
        .openPopup();

      mapInstanceRef.current = map;
    } catch (error) {
      console.error('地图初始化失败:', error);
      message.error('地图加载失败');
    }

    // 清理函数
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [contactInfo, currentTheme]); // 添加 currentTheme 依赖，主题变化时重新初始化地图

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh',
        flexDirection: 'column'
      }}>
        <Spin
          size="large"
          indicator={<LoadingOutlined style={{ fontSize: 48, color: 'var(--color-primary)' }} spin />}
        />
        <Paragraph style={{
          marginTop: '24px',
          color: 'var(--text-secondary)',
          fontSize: '16px'
        }}>
          正在加载联系信息...
        </Paragraph>
      </div>
    );
  }

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh',
        flexDirection: 'column'
      }}>
        <EnvironmentOutlined style={{ fontSize: 48, color: 'var(--color-error)', marginBottom: 16 }} />
        <Paragraph style={{
          color: 'var(--text-secondary)',
          fontSize: '16px',
          textAlign: 'center'
        }}>
          {error}
        </Paragraph>
      </div>
    );
  }

  // 如果没有数据，显示空状态
  if (!contactInfo) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh',
        flexDirection: 'column'
      }}>
        <EnvironmentOutlined style={{ fontSize: 48, color: 'var(--text-tertiary)', marginBottom: 16 }} />
        <Paragraph style={{
          color: 'var(--text-secondary)',
          fontSize: '16px'
        }}>
          暂无联系信息
        </Paragraph>
      </div>
    );
  }

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Title level={1} style={{
          margin: 0,
          color: 'var(--text-primary)',
          fontWeight: '700',
          fontSize: '3rem',
          letterSpacing: '-0.02em'
        }}>
          联系我们
        </Title>
        <Paragraph style={{
          fontSize: '20px',
          color: 'var(--text-secondary)',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0'
        }}>
          欢迎与我们联系，我们期待与您进行学术交流和合作
        </Paragraph>
      </div>

      <div style={{ width: '100%', maxWidth: '1200px', margin: '0 auto' }}>
        {/* 联系信息 */}
        <Card
          title="联系信息"
          style={{
            borderRadius: '20px',
            border: 'none',
            background: 'var(--gradient-secondary)',
            boxShadow: 'var(--shadow-md)',
            marginBottom: '32px'
          }}
          styles={{ body: { padding: '32px' } }}
        >
          <Row gutter={[32, 32]} justify="center">
            <Col xs={24} sm={12} lg={8}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
                gap: '16px'
              }}>
                <div style={{
                  width: '64px',
                  height: '64px',
                  borderRadius: '50%',
                  background: 'var(--color-primary)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '8px'
                }}>
                  <EnvironmentOutlined style={{
                    fontSize: 28,
                    color: 'white'
                  }} />
                </div>
                <strong style={{
                  color: 'var(--text-primary)',
                  fontSize: '18px'
                }}>地址</strong>
                <Paragraph style={{
                  margin: 0,
                  color: 'var(--text-secondary)',
                  lineHeight: '1.6'
                }}>
                  {contactInfo.address}
                </Paragraph>
              </div>
            </Col>

            <Col xs={24} sm={12} lg={8}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
                gap: '16px'
              }}>
                <div style={{
                  width: '64px',
                  height: '64px',
                  borderRadius: '50%',
                  background: 'var(--color-primary)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '8px'
                }}>
                  <MailOutlined style={{
                    fontSize: 28,
                    color: 'white'
                  }} />
                </div>
                <strong style={{
                  color: 'var(--text-primary)',
                  fontSize: '18px'
                }}>邮箱</strong>
                <Paragraph style={{
                  margin: 0,
                  color: 'var(--text-secondary)',
                  lineHeight: '1.6'
                }}>
                  <a
                    href={`mailto:${contactInfo.email}`}
                    style={{
                      color: 'var(--color-primary)',
                      textDecoration: 'none'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.textDecoration = 'underline';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.textDecoration = 'none';
                    }}
                  >
                    {contactInfo.email}
                  </a>
                </Paragraph>
              </div>
            </Col>
          </Row>
        </Card>

        {/* 交互式地图 */}
        <Card
          title="位置地图"
          style={{
            marginTop: 24,
            borderRadius: '20px',
            border: 'none',
            background: 'var(--gradient-secondary)',
            boxShadow: 'var(--shadow-md)'
          }}
          styles={{ body: { padding: '24px' } }}
        >
          <div
            ref={mapRef}
            style={{
              height: 400,
              borderRadius: '12px',
              overflow: 'hidden',
              border: '1px solid var(--border-primary)'
            }}
          />
          <Paragraph style={{
            textAlign: 'center',
            color: 'var(--text-tertiary)',
            marginTop: '16px',
            marginBottom: 0,
            fontSize: '14px'
          }}>
            © OpenStreetMap contributors
          </Paragraph>
        </Card>
      </div>
    </div>
  );
};

export default Contact; 
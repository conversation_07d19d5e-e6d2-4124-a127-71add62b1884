import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Tag, Spin, Alert, message, Button, Breadcrumb, Image, Row, Col, Divider, BackTop } from 'antd';
import {
  ArrowLeftOutlined,
  CalendarOutlined,
  DollarOutlined,
  HomeOutlined,
  ProjectOutlined,
  LoadingOutlined,
  FileImageOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { BlocksRenderer, type BlocksContent } from '@strapi/blocks-react-renderer';

import type { Project, StrapiBlocks } from '../../types';
import ProjectService from '../../api/project';

const { Title, Paragraph, Text } = Typography;

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // 获取项目详情
  useEffect(() => {
    const fetchProject = async () => {
      if (!id) {
        setError('项目ID无效');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const projectData = await ProjectService.getProjectById(parseInt(id));
        setProject(projectData);

        // 设置页面标题
        if (projectData.project_title) {
          document.title = `${projectData.project_title} - 科研项目 - GeoCUES Lab`;
        }


      } catch (error) {
        console.error('获取项目详情失败:', error);
        setError('获取项目详情失败，请稍后重试');
        message.error('获取项目详情失败');
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [id]);

  // 清理页面标题
  useEffect(() => {
    return () => {
      document.title = 'GeoCUES Lab – Geographic Computation for Urban Economy & Society';
    };
  }, []);

  // 使用官方 Strapi BlocksRenderer 渲染内容的组件
  const StrapiBlocksContent: React.FC<{
    content: StrapiBlocks | undefined;
    style?: React.CSSProperties;
  }> = ({ content, style }) => {
    if (!content || !Array.isArray(content)) {
      return <span style={{ color: 'var(--text-secondary)', fontStyle: 'italic' }}>暂无信息</span>;
    }

    // 将 StrapiBlocks 转换为 BlocksContent 格式
    const blocksContent = content as unknown as BlocksContent;

    const defaultStyle = {
      fontSize: '16px',
      color: 'var(--text-primary)',
      lineHeight: '1.7'
    };

    const combinedStyle = { ...defaultStyle, ...style };

    return (
      <div style={combinedStyle}>
        <BlocksRenderer 
          content={blocksContent}
          blocks={{
            // 自定义段落样式
            paragraph: ({ children }) => (
              <p style={{ 
                margin: '0 0 16px 0', 
                lineHeight: '1.7',
                fontSize: 'inherit',
                color: 'inherit'
              }}>
                {children}
              </p>
            ),
            // 自定义标题样式
            heading: ({ children, level }) => {
              const headingStyle = {
                margin: '24px 0 16px 0',
                color: 'var(--text-primary)',
                fontWeight: 600,
                lineHeight: '1.4'
              };
              
              switch (level) {
                case 1:
                  return <h1 style={{ ...headingStyle, fontSize: '28px' }}>{children}</h1>;
                case 2:
                  return <h2 style={{ ...headingStyle, fontSize: '24px' }}>{children}</h2>;
                case 3:
                  return <h3 style={{ ...headingStyle, fontSize: '20px' }}>{children}</h3>;
                case 4:
                  return <h4 style={{ ...headingStyle, fontSize: '18px' }}>{children}</h4>;
                case 5:
                  return <h5 style={{ ...headingStyle, fontSize: '16px' }}>{children}</h5>;
                case 6:
                  return <h6 style={{ ...headingStyle, fontSize: '14px' }}>{children}</h6>;
                default:
                  return <h2 style={{ ...headingStyle, fontSize: '24px' }}>{children}</h2>;
              }
            },
            // 自定义列表样式
            list: ({ children, format }) => {
              const listStyle = {
                margin: '16px 0',
                paddingLeft: '24px',
                color: 'inherit'
              };
              
              return format === 'ordered' ? (
                <ol style={listStyle}>{children}</ol>
              ) : (
                <ul style={listStyle}>{children}</ul>
              );
            },
            'list-item': ({ children }) => (
              <li style={{ margin: '8px 0', lineHeight: '1.6' }}>{children}</li>
            ),
            // 自定义引用样式
            quote: ({ children }) => (
              <blockquote style={{
                margin: '24px 0',
                padding: '16px 20px',
                borderLeft: '4px solid var(--color-primary)',
                background: 'var(--bg-tertiary)',
                borderRadius: '0 8px 8px 0',
                fontStyle: 'italic',
                color: 'var(--text-secondary)'
              }}>
                {children}
              </blockquote>
            ),
            // 自定义代码块样式
            code: ({ children }) => (
              <pre style={{
                margin: '16px 0',
                padding: '16px',
                background: 'var(--bg-tertiary)',
                borderRadius: '8px',
                overflow: 'auto',
                fontSize: '14px',
                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                border: '1px solid var(--border-secondary)'
              }}>
                <code>{children}</code>
              </pre>
            )
          }}
          modifiers={{
            bold: ({ children }) => <strong style={{ fontWeight: 600 }}>{children}</strong>,
            italic: ({ children }) => <em style={{ fontStyle: 'italic' }}>{children}</em>,
            underline: ({ children }) => <u style={{ textDecoration: 'underline' }}>{children}</u>,
            strikethrough: ({ children }) => <s style={{ textDecoration: 'line-through' }}>{children}</s>,
            code: ({ children }) => (
              <code style={{
                background: 'var(--bg-tertiary)',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '0.9em',
                fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                border: '1px solid var(--border-secondary)'
              }}>
                {children}
              </code>
            )
          }}
        />
      </div>
    );
  };

  // 获取项目图片URL
  const getProjectImageUrl = (project: Project): string | undefined => {
    // 检查项目图片对象是否存在
    if (!project.project_pic) {
      return undefined;
    }

    const image = project.project_pic;
    // 优先使用原图，因为详情页面需要更高质量的图片
    const url = image.url;

    // 如果是相对路径，添加API基础URL
    if (url && url.startsWith('/')) {
      return `${import.meta.env.VITE_API_URL || 'http://localhost:1337'}${url}`;
    }

    return url;
  };

  // 返回按钮处理
  const handleGoBack = () => {
    navigate('/projects');
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="apple-fade-in" style={{ width: '100%' }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px',
          gap: '16px'
        }}>
          <Spin
            size="large"
            indicator={<LoadingOutlined style={{ fontSize: 48, color: 'var(--color-primary)' }} spin />}
          />
          <Text style={{ color: 'var(--text-secondary)', fontSize: '16px' }}>
            正在加载项目详情...
          </Text>
        </div>
      </div>
    );
  }

  // 如果有错误，显示错误状态
  if (error || !project) {
    return (
      <div className="apple-fade-in" style={{ width: '100%' }}>
        <Alert
          message="加载失败"
          description={error || '项目不存在'}
          type="error"
          showIcon
          style={{
            borderRadius: '16px',
            border: 'none',
            boxShadow: 'var(--shadow-md)'
          }}
          action={
            <Button type="primary" onClick={handleGoBack}>
              返回项目列表
            </Button>
          }
        />
      </div>
    );
  }

  const imageUrl = getProjectImageUrl(project);

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      {/* 面包屑导航 */}
      <div style={{ marginBottom: '24px' }}>
        <Breadcrumb
          style={{ fontSize: '14px' }}
          items={[
            {
              href: '/',
              title: (
                <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <HomeOutlined />
                  <span>首页</span>
                </span>
              ),
            },
            {
              href: '/projects',
              title: (
                <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <ProjectOutlined />
                  <span>科研项目</span>
                </span>
              ),
            },
            {
              title: project.project_title,
            },
          ]}
        />
      </div>

      {/* 返回按钮 */}
      <div style={{ marginBottom: '32px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={handleGoBack}
          style={{
            borderRadius: '8px',
            border: '1px solid var(--border-secondary)',
            background: 'var(--bg-secondary)',
            color: 'var(--text-primary)'
          }}
        >
          返回项目列表
        </Button>
      </div>

      {/* 项目详情卡片 */}
      <Card
        style={{
          borderRadius: '16px',
          border: 'none',
          background: 'var(--bg-card)',
          boxShadow: 'var(--shadow-md)'
        }}
        styles={{ body: { padding: '32px' } }}
      >
        {/* 项目图片 - 顶部显示 */}
        {imageUrl && (
          <div style={{
            marginBottom: '32px',
            display: 'flex',
            justifyContent: 'center'
          }}>
            <div style={{
              borderRadius: '12px',
              overflow: 'hidden',
              background: 'var(--bg-tertiary)',
              maxWidth: '800px',
              width: '100%',
              minHeight: '300px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Image
                src={imageUrl}
                alt={project.project_title}
                style={{
                  width: '100%',
                  height: 'auto',
                  objectFit: 'cover'
                }}
                preview={{
                  mask: (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px',
                      color: 'white'
                    }}>
                      <EyeOutlined />
                      <span>查看大图</span>
                    </div>
                  )
                }}
              />
            </div>
          </div>
        )}

        {/* 项目信息 - 图片下方显示 */}
        <div>
            {/* 项目标题 */}
            <Title level={1} style={{
              margin: '0 0 24px 0',
              color: 'var(--text-primary)',
              fontSize: '32px',
              fontWeight: '700',
              lineHeight: '1.3'
            }}>
              {project.project_title}
            </Title>

            {/* 项目基本信息 */}
            <div style={{ marginBottom: '32px' }}>
              <Row gutter={[16, 16]}>
                {/* 资助机构 */}
                <Col xs={24} sm={12}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    padding: '16px',
                    background: 'var(--bg-tertiary)',
                    borderRadius: '12px'
                  }}>
                    <DollarOutlined style={{
                      fontSize: '20px',
                      color: 'var(--color-primary)'
                    }} />
                    <div>
                      <Text style={{
                        display: 'block',
                        fontSize: '12px',
                        color: 'var(--text-tertiary)',
                        marginBottom: '4px'
                      }}>
                        资助机构
                      </Text>
                      <Text style={{
                        fontSize: '16px',
                        fontWeight: '600',
                        color: 'var(--text-primary)'
                      }}>
                        {project.funding_agency}
                      </Text>
                    </div>
                  </div>
                </Col>

                {/* 资助年份 */}
                {project.funding_year && (
                  <Col xs={24} sm={12}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      padding: '16px',
                      background: 'var(--bg-tertiary)',
                      borderRadius: '12px'
                    }}>
                      <CalendarOutlined style={{
                        fontSize: '20px',
                        color: 'var(--color-secondary)'
                      }} />
                      <div>
                        <Text style={{
                          display: 'block',
                          fontSize: '12px',
                          color: 'var(--text-tertiary)',
                          marginBottom: '4px'
                        }}>
                          资助年份
                        </Text>
                        <Text style={{
                          fontSize: '16px',
                          fontWeight: '600',
                          color: 'var(--text-primary)'
                        }}>
                          {project.funding_year}年
                        </Text>
                      </div>
                    </div>
                  </Col>
                )}
              </Row>

              {/* 资助类型标签 */}
              {project.funding_type && (
                <div style={{ marginTop: '16px' }}>
                  <Tag
                    color="blue"
                    style={{
                      borderRadius: '16px',
                      padding: '8px 16px',
                      fontSize: '14px',
                      fontWeight: '500',
                      border: 'none',
                      background: 'var(--color-primary)',
                      color: 'white'
                    }}
                  >
                    {project.funding_type}
                  </Tag>
                </div>
              )}
            </div>

            <Divider style={{ margin: '32px 0' }} />

            {/* 项目摘要内容 */}
            {project.project_abs && project.project_abs.length > 0 && (
              <div style={{
                background: 'var(--bg-tertiary)',
                padding: '24px',
                borderRadius: '12px',
                border: '1px solid var(--border-secondary)',
                lineHeight: '1.8',
                color: 'var(--text-primary)'
              }}>
                <StrapiBlocksContent content={project.project_abs} />
              </div>
            )}
        </div>
      </Card>

      {/* 返回顶部按钮 */}
      <BackTop
        style={{
          right: '32px',
          bottom: '32px'
        }}
        target={() => window}
      >
        <div style={{
          width: '48px',
          height: '48px',
          borderRadius: '24px',
          background: 'var(--color-primary)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: 'var(--shadow-lg)',
          color: 'white',
          fontSize: '20px',
          cursor: 'pointer',
          transition: 'all 0.3s ease'
        }}>
          ↑
        </div>
      </BackTop>
    </div>
  );
};

export default ProjectDetail;

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Row, Col, Typography, Tag, Spin, Alert, message, Input, Select, Button, Badge, Image, Pagination } from 'antd';
import {
  ProjectOutlined,
  CalendarOutlined,
  DollarOutlined,
  SearchOutlined,
  FilterOutlined,
  LoadingOutlined,
  FileImageOutlined,
  EyeOutlined
} from '@ant-design/icons';

import type { Project } from '../../types';
import ProjectService from '../../api/project';

const { Title, Paragraph, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const Projects: React.FC = () => {
  const navigate = useNavigate();

  // 状态管理
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<number | undefined>(undefined);
  const [selectedAgency, setSelectedAgency] = useState<string | undefined>(undefined);
  const [selectedType, setSelectedType] = useState<string | undefined>(undefined);
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [availableAgencies, setAvailableAgencies] = useState<string[]>([]);
  const [availableTypes, setAvailableTypes] = useState<string[]>([]);

  // 分页状态
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(12); // 每页显示12个项目

  // 获取项目数据
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null);

        // 并行获取项目数据和筛选选项
        const [projectsData, yearsData, agenciesData, typesData] = await Promise.all([
          ProjectService.getAllProjects({
            pageSize: 1000, // 获取所有项目
            sort: 'funding_year:desc,createdAt:desc'
          }),
          ProjectService.getAvailableYears(),
          ProjectService.getAvailableAgencies(),
          ProjectService.getAvailableTypes()
        ]);

        setProjects(projectsData);
        setFilteredProjects(projectsData);
        setAvailableYears(yearsData);
        setAvailableAgencies(agenciesData);
        setAvailableTypes(typesData);


      } catch (error) {
        console.error('获取项目数据失败:', error);
        setError('获取项目数据失败，请稍后重试');
        message.error('获取项目数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // 过滤项目数据
  useEffect(() => {
    let filtered = projects;

    // 按搜索关键词过滤
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(project =>
        project.project_title?.toLowerCase().includes(term) ||
        project.funding_agency?.toLowerCase().includes(term) ||
        project.funding_type?.toLowerCase().includes(term)
      );
    }

    // 按年份过滤
    if (selectedYear) {
      filtered = filtered.filter(project => project.funding_year === selectedYear);
    }

    // 按资助机构过滤
    if (selectedAgency) {
      filtered = filtered.filter(project => project.funding_agency === selectedAgency);
    }

    // 按资助类型过滤
    if (selectedType) {
      filtered = filtered.filter(project => project.funding_type === selectedType);
    }

    setFilteredProjects(filtered);
    // 重置分页到第一页
    setCurrentPage(1);
  }, [projects, searchTerm, selectedYear, selectedAgency, selectedType]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  // 处理年份选择
  const handleYearChange = (year: number | undefined) => {
    setSelectedYear(year);
  };

  // 处理资助机构选择
  const handleAgencyChange = (agency: string | undefined) => {
    setSelectedAgency(agency);
  };

  // 处理资助类型选择
  const handleTypeChange = (type: string | undefined) => {
    setSelectedType(type);
  };

  // 清除所有筛选条件
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedYear(undefined);
    setSelectedAgency(undefined);
    setSelectedType(undefined);
  };

  // 获取项目图片URL
  const getProjectImageUrl = (project: Project): string | undefined => {
    // 检查项目图片对象是否存在
    if (!project.project_pic) {
      return undefined;
    }

    const image = project.project_pic;
    // 优先使用small格式，如果没有则使用原图
    const url = image.formats?.small?.url || image.url;

    // 如果是相对路径，添加API基础URL
    if (url && url.startsWith('/')) {
      return `${import.meta.env.VITE_API_URL || 'http://localhost:1337'}${url}`;
    }

    return url;
  };

  // 渲染单个项目卡片
  const renderProjectCard = (project: Project) => {
    const imageUrl = getProjectImageUrl(project);

    return (
      <Col xs={24} sm={12} lg={8} key={project.id}>
        <Card
          hoverable
          style={{
            borderRadius: '16px',
            border: 'none',
            background: 'var(--bg-card)',
            boxShadow: 'var(--shadow-md)',
            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'translateY(-4px)';
            e.currentTarget.style.boxShadow = 'var(--shadow-lg)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.boxShadow = 'var(--shadow-md)';
          }}
          styles={{
            body: {
              padding: '24px',
              display: 'flex',
              flexDirection: 'column',
              height: '100%'
            }
          }}
          cover={
            imageUrl ? (
              <div style={{
                height: '200px',
                overflow: 'hidden',
                borderRadius: '16px 16px 0 0',
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'var(--bg-secondary)'
              }}>
                <Image
                  src={imageUrl}
                  alt={project.project_title}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                  preview={{
                    mask: (
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '8px',
                        color: 'white'
                      }}>
                        <EyeOutlined />
                        <span>预览</span>
                      </div>
                    )
                  }}
                />
              </div>
            ) : (
              <div style={{
                height: '200px',
                background: 'var(--gradient-secondary)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '16px 16px 0 0'
              }}>
                <FileImageOutlined style={{
                  fontSize: '48px',
                  color: 'var(--text-tertiary)'
                }} />
              </div>
            )
          }
          actions={[
            <Button
              key="view"
              type="primary"
              icon={<EyeOutlined />}
              style={{
                borderRadius: '8px',
                background: 'var(--color-primary)',
                borderColor: 'var(--color-primary)'
              }}
              onClick={() => {
                navigate(`/projects/${project.id}`);
              }}
            >
              查看详情
            </Button>
          ]}
        >
          {/* 项目标题 */}
          <Title level={4} style={{
            margin: '0 0 16px 0',
            color: 'var(--text-primary)',
            fontSize: '18px',
            fontWeight: '600',
            lineHeight: '1.4',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden'
          }}>
            {project.project_title}
          </Title>

          {/* 资助机构 */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            marginBottom: '12px',
            gap: '8px'
          }}>
            <DollarOutlined style={{ color: 'var(--color-primary)' }} />
            <Text style={{
              color: 'var(--text-secondary)',
              fontSize: '14px',
              fontWeight: '500'
            }}>
              {project.funding_agency}
            </Text>
          </div>

          {/* 资助年份 */}
          {project.funding_year && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '12px',
              gap: '8px'
            }}>
              <CalendarOutlined style={{ color: 'var(--color-secondary)' }} />
              <Text style={{
                color: 'var(--text-secondary)',
                fontSize: '14px'
              }}>
                {project.funding_year}年
              </Text>
            </div>
          )}

          {/* 资助类型标签 */}
          {project.funding_type && (
            <div style={{ marginBottom: '16px' }}>
              <Tag
                color="blue"
                style={{
                  borderRadius: '12px',
                  padding: '4px 12px',
                  fontSize: '12px',
                  fontWeight: '500',
                  border: 'none',
                  background: 'var(--color-primary)',
                  color: 'white'
                }}
              >
                {project.funding_type}
              </Tag>
            </div>
          )}


        </Card>
      </Col>
    );
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="apple-fade-in" style={{ width: '100%' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px'
        }}>
          <Spin
            size="large"
            indicator={<LoadingOutlined style={{ fontSize: 48, color: 'var(--color-primary)' }} spin />}
          />
        </div>
      </div>
    );
  }

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className="apple-fade-in" style={{ width: '100%' }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          style={{
            borderRadius: '16px',
            border: 'none',
            boxShadow: 'var(--shadow-md)'
          }}
        />
      </div>
    );
  }

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      {/* 页面标题 */}
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Badge
          count={<ProjectOutlined style={{ color: 'var(--color-primary)' }} />}
          style={{ backgroundColor: 'transparent' }}
        >
          <Title level={1} style={{
            margin: 0,
            color: 'var(--text-primary)',
            fontWeight: '700',
            fontSize: '3rem',
            letterSpacing: '-0.02em'
          }}>
            科研项目
          </Title>
        </Badge>
        <Paragraph style={{
          fontSize: '20px',
          color: 'var(--text-secondary)',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '600px',
          margin: '20px auto 0'
        }}>
          我们课题组承担了多项重要科研项目，涵盖人工智能、地理计算等前沿领域
        </Paragraph>
      </div>

      {/* 搜索和过滤区域 */}
      <div style={{
        marginBottom: '32px',
        padding: '24px',
        background: 'var(--gradient-secondary)',
        borderRadius: '16px',
        boxShadow: 'var(--shadow-md)'
      }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索项目标题、资助机构..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={8} md={4}>
            <Select
              placeholder="选择年份"
              allowClear
              size="large"
              value={selectedYear}
              onChange={handleYearChange}
              style={{ width: '100%' }}
              suffixIcon={<FilterOutlined />}
            >
              {availableYears.map(year => (
                <Option key={year} value={year}>{year}年</Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={8} md={4}>
            <Select
              placeholder="资助机构"
              allowClear
              size="large"
              value={selectedAgency}
              onChange={handleAgencyChange}
              style={{ width: '100%' }}
              suffixIcon={<FilterOutlined />}
            >
              {availableAgencies.map(agency => (
                <Option key={agency} value={agency}>{agency}</Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={8} md={4}>
            <Select
              placeholder="资助类型"
              allowClear
              size="large"
              value={selectedType}
              onChange={handleTypeChange}
              style={{ width: '100%' }}
              suffixIcon={<FilterOutlined />}
            >
              {availableTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={24} md={4}>
            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              alignItems: 'center',
              gap: '12px'
            }}>
              {(searchTerm || selectedYear || selectedAgency || selectedType) && (
                <Button
                  type="link"
                  onClick={clearFilters}
                  style={{ padding: 0, height: 'auto' }}
                >
                  清除筛选
                </Button>
              )}
              <span style={{
                color: 'var(--text-secondary)',
                fontSize: '14px'
              }}>
                共 {filteredProjects.length} 个项目
              </span>
            </div>
          </Col>
        </Row>
      </div>

      {/* 项目列表 */}
      <div style={{ width: '100%' }}>
        {filteredProjects.length > 0 ? (
          <div className="apple-fade-in">
            <Row gutter={[24, 24]}>
              {filteredProjects
                .slice((currentPage - 1) * pageSize, currentPage * pageSize)
                .map(renderProjectCard)}
            </Row>

            {/* 分页组件 */}
            {filteredProjects.length > pageSize && (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                marginTop: '48px'
              }}>
                <Pagination
                  current={currentPage}
                  total={filteredProjects.length}
                  pageSize={pageSize}
                  onChange={(page) => {
                    setCurrentPage(page);
                    // 滚动到页面顶部
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={(total, range) =>
                    `第 ${range[0]}-${range[1]} 项，共 ${total} 个项目`
                  }
                  className="projects-pagination"
                />
              </div>
            )}
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '64px 32px',
            color: 'var(--text-secondary)'
          }}>
            <ProjectOutlined style={{
              fontSize: '48px',
              color: 'var(--text-tertiary)',
              marginBottom: '16px'
            }} />
            <Title level={4} style={{ color: 'var(--text-secondary)', margin: '0 0 8px 0' }}>
              暂无项目数据
            </Title>
            <Paragraph style={{ color: 'var(--text-secondary)', margin: 0 }}>
              {searchTerm || selectedYear || selectedAgency || selectedType ? '没有找到符合条件的项目' : '暂时还没有项目数据'}
            </Paragraph>
          </div>
        )}
      </div>

      {/* 统计信息 */}
      {projects.length > 0 && (
        <Card
          title={
            <span style={{ color: 'var(--text-primary)', fontSize: '18px', fontWeight: '600' }}>
              项目统计
            </span>
          }
          style={{
            marginTop: '48px',
            borderRadius: '16px',
            border: 'none',
            background: 'var(--bg-card)',
            boxShadow: 'var(--shadow-md)'
          }}
          styles={{ body: { padding: '24px' } }}
        >
          <Row gutter={[24, 24]}>
            <Col xs={12} md={6}>
              <Card size="small" style={{
                textAlign: 'center',
                borderRadius: '12px',
                border: '1px solid var(--border-secondary)',
                background: 'var(--bg-secondary)'
              }}>
                <Title level={3} style={{ color: 'var(--color-primary)', margin: 0 }}>
                  {projects.length}
                </Title>
                <Paragraph style={{ margin: 0, color: 'var(--text-secondary)' }}>
                  总项目数
                </Paragraph>
              </Card>
            </Col>
            <Col xs={12} md={6}>
              <Card size="small" style={{
                textAlign: 'center',
                borderRadius: '12px',
                border: '1px solid var(--border-secondary)',
                background: 'var(--bg-secondary)'
              }}>
                <Title level={3} style={{ color: 'var(--color-success)', margin: 0 }}>
                  {availableAgencies.length}
                </Title>
                <Paragraph style={{ margin: 0, color: 'var(--text-secondary)' }}>
                  资助机构
                </Paragraph>
              </Card>
            </Col>
            <Col xs={12} md={6}>
              <Card size="small" style={{
                textAlign: 'center',
                borderRadius: '12px',
                border: '1px solid var(--border-secondary)',
                background: 'var(--bg-secondary)'
              }}>
                <Title level={3} style={{ color: 'var(--color-warning)', margin: 0 }}>
                  {availableTypes.length}
                </Title>
                <Paragraph style={{ margin: 0, color: 'var(--text-secondary)' }}>
                  资助类型
                </Paragraph>
              </Card>
            </Col>
            <Col xs={12} md={6}>
              <Card size="small" style={{
                textAlign: 'center',
                borderRadius: '12px',
                border: '1px solid var(--border-secondary)',
                background: 'var(--bg-secondary)'
              }}>
                <Title level={3} style={{ color: 'var(--color-secondary)', margin: 0 }}>
                  {availableYears.length}
                </Title>
                <Paragraph style={{ margin: 0, color: 'var(--text-secondary)' }}>
                  涵盖年份
                </Paragraph>
              </Card>
            </Col>
          </Row>
        </Card>
      )}
    </div>
  );
};

export default Projects;
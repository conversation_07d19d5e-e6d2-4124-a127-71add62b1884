import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Typography, Tag, Spin, Alert, message, But<PERSON>, Breadcrumb, Row, Col, Divider, BackTop } from 'antd';
import {
  ArrowLeftOutlined,
  CalendarOutlined,
  HomeOutlined,
  FileTextOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import { BlocksRenderer, type BlocksContent } from '@strapi/blocks-react-renderer';

import type { NewsItem, StrapiBlocks } from '../../types';
import NewsService from '../../api/news';

const { Title, Paragraph, Text } = Typography;

const NewsDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [news, setNews] = useState<NewsItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取新闻详情
  useEffect(() => {
    const fetchNews = async () => {
      if (!id) {
        setError('新闻ID无效');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        const newsData = await NewsService.getNewsById(parseInt(id));
        setNews(newsData);

        // 设置页面标题
        if (newsData.title) {
          document.title = `${newsData.title} - 新闻动态 - GeoCUES Lab`;
        }

      } catch (error) {
        console.error('获取新闻详情失败:', error);
        setError('获取新闻详情失败，请稍后重试');
        message.error('获取新闻详情失败');
      } finally {
        setLoading(false);
      }
    };

    fetchNews();
  }, [id]);

  // 清理页面标题
  useEffect(() => {
    return () => {
      document.title = 'GeoCUES Lab – Geographic Computation for Urban Economy & Society';
    };
  }, []);

  // 格式化日期显示
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  // 获取类型标签颜色
  const getTypeColor = (type: string): string => {
    const colors = ['blue', 'green', 'orange', 'purple', 'red', 'cyan', 'magenta', 'gold'];
    const index = type.length % colors.length;
    return colors[index];
  };

  // 返回按钮处理
  const handleGoBack = () => {
    navigate('/news');
  };

  // Strapi Blocks 内容渲染组件
  const StrapiBlocksContent: React.FC<{ content: StrapiBlocks }> = ({ content }) => {
    if (!content || content.length === 0) {
      return (
        <div style={{
          textAlign: 'center',
          padding: '40px',
          color: 'var(--text-tertiary)',
          fontStyle: 'italic'
        }}>
          暂无详细内容
        </div>
      );
    }

    return (
      <div style={{
        color: 'var(--text-primary)',
        lineHeight: '1.8',
        fontSize: '16px'
      }}>
        <BlocksRenderer content={content as BlocksContent} />
      </div>
    );
  };

  // 加载状态
  if (loading) {
    return (
      <div className="apple-fade-in" style={{ 
        width: '100%', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin 
          size="large" 
          indicator={<LoadingOutlined style={{ fontSize: 48, color: 'var(--color-primary)' }} spin />}
        />
      </div>
    );
  }

  // 错误状态
  if (error || !news) {
    return (
      <div className="apple-fade-in" style={{ width: '100%' }}>
        <Alert
          message="加载失败"
          description={error || '未找到指定的新闻'}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={handleGoBack}>
              返回新闻列表
            </Button>
          }
          style={{
            borderRadius: '12px',
            border: '1px solid var(--border-secondary)'
          }}
        />
      </div>
    );
  }

  return (
    <div className="apple-fade-in" style={{ width: '100%' }}>
      {/* 面包屑导航 */}
      <div style={{ marginBottom: '24px' }}>
        <Breadcrumb
          style={{ fontSize: '14px' }}
          items={[
            {
              href: '/',
              title: (
                <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <HomeOutlined />
                  <span>首页</span>
                </span>
              ),
            },
            {
              href: '/news',
              title: (
                <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <FileTextOutlined />
                  <span>新闻动态</span>
                </span>
              ),
            },
            {
              title: news.title,
            },
          ]}
        />
      </div>

      {/* 返回按钮 */}
      <div style={{ marginBottom: '24px' }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={handleGoBack}
          style={{
            borderRadius: '8px',
            border: '1px solid var(--border-secondary)',
            color: 'var(--text-secondary)'
          }}
        >
          返回新闻列表
        </Button>
      </div>

      {/* 新闻详情卡片 */}
      <Card
        style={{
          borderRadius: '16px',
          border: '1px solid var(--border-secondary)',
          background: 'var(--bg-card)',
          boxShadow: 'var(--shadow-sm)',
          transition: 'all 0.3s ease'
        }}
        styles={{ body: { padding: '32px' } }}
      >
        <div style={{ marginBottom: '32px' }}>
          <Row gutter={[24, 24]}>
            <Col span={24}>
              {/* 新闻类型标签 */}
              <div style={{ marginBottom: '16px' }}>
                <Tag
                  color={getTypeColor(news.type)}
                  style={{
                    borderRadius: '16px',
                    padding: '8px 16px',
                    fontSize: '14px',
                    fontWeight: '500',
                    border: 'none'
                  }}
                >
                  {news.type}
                </Tag>
              </div>

              {/* 新闻标题 */}
              <Title
                level={1}
                style={{
                  color: 'var(--text-primary)',
                  fontSize: '2.5rem',
                  fontWeight: '700',
                  lineHeight: '1.2',
                  margin: '0 0 24px 0',
                  letterSpacing: '-0.02em',
                  transition: 'color 0.3s ease'
                }}
              >
                {news.title}
              </Title>

              {/* 新闻日期 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                color: 'var(--text-secondary)',
                fontSize: '16px',
                marginBottom: '24px'
              }}>
                <CalendarOutlined style={{ marginRight: '8px' }} />
                <span>{formatDate(news.date)}</span>
              </div>

              {/* 新闻摘要 */}
              <div style={{
                background: 'var(--bg-tertiary)',
                padding: '24px',
                borderRadius: '12px',
                border: '1px solid var(--border-secondary)',
                marginBottom: '32px'
              }}>
                <Text style={{
                  fontSize: '18px',
                  lineHeight: '1.6',
                  color: 'var(--text-primary)',
                  fontWeight: '500'
                }}>
                  {news.abs}
                </Text>
              </div>
            </Col>
          </Row>

          <Divider style={{ margin: '32px 0' }} />

          {/* 新闻详情内容 */}
          {news.detail && news.detail.length > 0 && (
            <div style={{
              background: 'var(--bg-tertiary)',
              padding: '24px',
              borderRadius: '12px',
              border: '1px solid var(--border-secondary)',
              lineHeight: '1.8',
              color: 'var(--text-primary)'
            }}>
              <StrapiBlocksContent content={news.detail} />
            </div>
          )}
        </div>
      </Card>

      {/* 回到顶部按钮 */}
      <BackTop style={{ right: 24, bottom: 24 }} />
    </div>
  );
};

export default NewsDetail;

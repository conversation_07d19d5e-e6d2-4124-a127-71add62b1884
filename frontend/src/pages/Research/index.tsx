import React, { useState, useEffect } from 'react';
import { Card, Typography, Tag, Space, Image, Badge, Spin, Alert, message, Modal } from 'antd';
import {
  ExperimentOutlined,
  LoadingOutlined,
  CloseOutlined
} from '@ant-design/icons';
import type { ResearchTopic } from '../../types';
import ResearchTopicsService from '../../api/researchTopics';

const { Title, Paragraph } = Typography;

const Research: React.FC = () => {
  // 状态管理
  const [researchTopics, setResearchTopics] = useState<ResearchTopic[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<ResearchTopic | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);

  // 获取研究主题数据
  useEffect(() => {
    const fetchResearchTopics = async () => {
      try {
        setLoading(true);
        setError(null);

        const topics = await ResearchTopicsService.getAllResearchTopics();
        setResearchTopics(topics);
      } catch (error) {
        console.error('获取研究主题数据失败:', error);
        setError('获取研究主题数据失败，请稍后重试');
        message.error('获取研究主题数据失败');
      } finally {
        setLoading(false);
      }
    };

    fetchResearchTopics();
  }, []);

  // 获取图片 URL 的辅助函数 - 适配 Strapi v5 结构
  const getImageUrl = (topic: ResearchTopic): string | undefined => {
    if (topic.image) {
      // 优先使用 medium 格式，如果没有则使用原图
      const url = topic.image.formats?.medium?.url || topic.image.url;
      // 如果是相对路径，添加 API 基础 URL
      if (url.startsWith('/')) {
        return `${import.meta.env.VITE_API_URL || 'http://localhost:1337'}${url}`;
      }
      return url;
    }
    return undefined;
  };

  // 分割关键词的辅助函数
  const splitKeywords = (keywords: string): string[] => {
    return keywords.split('、').filter(keyword => keyword.trim() !== '');
  };

  // 处理研究主题点击事件
  const handleTopicClick = (topic: ResearchTopic) => {
    setSelectedTopic(topic);
    setModalVisible(true);
  };

  // 关闭弹窗
  const handleModalClose = () => {
    setModalVisible(false);
    setSelectedTopic(null);
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh',
        flexDirection: 'column'
      }}>
        <Spin
          size="large"
          indicator={<LoadingOutlined style={{ fontSize: 48, color: 'var(--color-primary)' }} spin />}
        />
        <Paragraph style={{
          marginTop: '24px',
          color: 'var(--text-secondary)',
          fontSize: '16px'
        }}>
          正在加载研究主题信息...
        </Paragraph>
      </div>
    );
  }

  // 如果有错误，显示错误信息
  if (error) {
    return (
      <div className="apple-fade-in" style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '60vh'
      }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          action={
            <button
              onClick={() => window.location.reload()}
              style={{
                background: 'var(--color-primary)',
                color: 'var(--text-inverse)',
                border: 'none',
                borderRadius: '6px',
                padding: '8px 16px',
                cursor: 'pointer'
              }}
            >
              重新加载
            </button>
          }
        />
      </div>
    );
  }

  return (
    <div className="apple-fade-in">
      {/* 页面标题 */}
      <div style={{ textAlign: 'center', marginBottom: '64px' }}>
        <Badge
          count={<ExperimentOutlined style={{ color: 'var(--color-primary)' }} />}
          style={{ backgroundColor: 'transparent' }}
        >
          <Title level={1} style={{
            margin: 0,
            color: 'var(--text-primary)',
            fontWeight: '700',
            fontSize: '3rem',
            letterSpacing: '-0.02em'
          }}>
            研究方向
          </Title>
        </Badge>
        <Paragraph style={{
          fontSize: '20px',
          color: 'var(--text-secondary)',
          marginTop: '20px',
          marginBottom: 0,
          lineHeight: '1.6',
          maxWidth: '700px',
          margin: '20px auto 0'
        }}>
          我们课题组在地理计算与城市经济社会的多个前沿领域开展深入研究，致力于解决实际应用中的关键问题
        </Paragraph>
      </div>

      {/* 研究方向网格 */}
      <div className="apple-grid" style={{ gap: '32px', marginBottom: '80px', width: '100%' }}>
        {researchTopics.map((topic) => {
          const keywords = splitKeywords(topic.keywords);
          return (
            <Card
              key={topic.id}
              style={{
                borderRadius: '24px',
                border: 'none',
                background: 'var(--gradient-secondary)',
                boxShadow: 'var(--shadow-md)',
                transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                cursor: 'pointer',
                overflow: 'hidden',
                height: '100%'
              }}
              hoverable
              styles={{ body: { padding: 0 } }}
              onClick={() => handleTopicClick(topic)}
            >
              {/* 图片区域 */}
              <div style={{ position: 'relative', overflow: 'hidden' }}>
                <Image
                  src={getImageUrl(topic) || 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop'}
                  alt={topic.title}
                  style={{
                    width: '100%',
                    height: '240px',
                    objectFit: 'cover'
                  }}
                  preview={false}
                />
                <div style={{
                  position: 'absolute',
                  top: '20px',
                  left: '20px',
                  width: '48px',
                  height: '48px',
                  borderRadius: '12px',
                  background: 'var(--color-primary)20',
                  backdropFilter: 'blur(10px)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '20px',
                  color: 'var(--color-primary)'
                }}>
                  <ExperimentOutlined />
                </div>
              </div>

            {/* 内容区域 */}
            <div style={{ padding: '32px' }}>
              <Title level={3} style={{
                margin: '0 0 16px 0',
                color: 'var(--text-primary)',
                fontWeight: '600',
                fontSize: '1.5rem'
              }}>
                {topic.title}
              </Title>

              <Paragraph style={{
                fontSize: '15px',
                color: 'var(--text-secondary)',
                lineHeight: '1.6',
                marginBottom: '20px',
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}>
                {topic.description}
              </Paragraph>

              {/* 关键词标签 */}
              <div style={{ marginBottom: '20px' }}>
                <Space wrap size={[8, 8]}>
                  {keywords.slice(0, 4).map((keyword, index) => (
                    <Tag
                      key={index}
                      style={{
                        background: 'var(--color-primary-light)',
                        color: 'var(--color-primary)',
                        border: '1px solid var(--color-primary-border)',
                        borderRadius: '20px',
                        padding: '4px 12px',
                        fontSize: '12px',
                        fontWeight: '500'
                      }}
                    >
                      {keyword}
                    </Tag>
                  ))}
                  {keywords.length > 4 && (
                    <Tag
                      style={{
                        background: 'var(--bg-secondary)',
                        color: 'var(--text-secondary)',
                        border: '1px solid var(--border-color)',
                        borderRadius: '20px',
                        padding: '4px 12px',
                        fontSize: '12px'
                      }}
                    >
                      +{keywords.length - 4}
                    </Tag>
                  )}
                </Space>
              </div>
            </div>
          </Card>
          );
        })}
      </div>

      {/* 研究主题详情模态框 */}
      <Modal
        title={null}
        open={modalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        style={{ top: 20 }}
        styles={{
          content: {
            borderRadius: '20px',
            overflow: 'hidden'
          }
        }}
        closeIcon={<CloseOutlined style={{ color: 'var(--text-secondary)' }} />}
      >
        {selectedTopic && (
          <div>
            {/* 头部图片 */}
            <div style={{ position: 'relative', marginBottom: '32px' }}>
              <Image
                src={getImageUrl(selectedTopic) || 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=800&h=300&fit=crop'}
                alt={selectedTopic.title}
                style={{
                  width: '100%',
                  height: '300px',
                  objectFit: 'cover'
                }}
                preview={false}
              />
              <div style={{
                position: 'absolute',
                bottom: '24px',
                left: '24px',
                right: '24px',
                background: 'var(--bg-card)',
                backdropFilter: 'blur(10px)',
                borderRadius: '16px',
                padding: '24px'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <div style={{
                    width: '48px',
                    height: '48px',
                    borderRadius: '12px',
                    background: 'var(--color-primary-light)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '20px',
                    color: 'var(--color-primary)',
                    marginRight: '16px'
                  }}>
                    <ExperimentOutlined />
                  </div>
                  <Title level={2} style={{
                    margin: 0,
                    color: 'var(--text-primary)',
                    fontWeight: '600'
                  }}>
                    {selectedTopic.title}
                  </Title>
                </div>
              </div>
            </div>

            <div style={{ padding: '0 24px 24px' }}>
              {/* 详细描述 */}
              <div style={{ marginBottom: '32px' }}>
                <Paragraph style={{
                  fontSize: '16px',
                  color: 'var(--text-primary)',
                  lineHeight: '1.7',
                  margin: 0
                }}>
                  {selectedTopic.description}
                </Paragraph>
              </div>

              {/* 关键词 */}
              <div style={{ marginBottom: '32px' }}>
                <Title level={4} style={{
                  color: 'var(--text-primary)',
                  marginBottom: '16px'
                }}>
                  关键领域
                </Title>
                <Space wrap size={[12, 12]}>
                  {splitKeywords(selectedTopic.keywords).map((keyword, index) => (
                    <Tag
                      key={index}
                      style={{
                        background: 'var(--color-primary-light)',
                        color: 'var(--color-primary)',
                        border: '1px solid var(--color-primary-border)',
                        borderRadius: '20px',
                        padding: '6px 16px',
                        fontSize: '14px',
                        fontWeight: '500'
                      }}
                    >
                      {keyword}
                    </Tag>
                  ))}
                </Space>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Research; 
import api from './config';
import type { Project, StrapiProjectResponse, PaginationParams } from '../types';

/**
 * 科研项目 API 服务
 */
class ProjectService {
  /**
   * 获取所有科研项目
   * @param params 分页和过滤参数
   * @returns 科研项目列表
   */
  async getAllProjects(params?: PaginationParams): Promise<Project[]> {
    try {
      const queryParams = new URLSearchParams();
      
      // 设置分页参数
      if (params?.page) {
        queryParams.append('pagination[page]', params.page.toString());
      }
      if (params?.pageSize) {
        queryParams.append('pagination[pageSize]', params.pageSize.toString());
      }
      
      // 设置排序参数 - 默认按资助年份降序，然后按创建时间降序
      const sortParam = params?.sort || 'funding_year:desc,createdAt:desc';
      queryParams.append('sort', sortParam);
      
      // 设置过滤参数
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            queryParams.append(`filters[${key}]`, value.toString());
          }
        });
      }
      
      // 只获取已发布的内容
      queryParams.append('publicationState', 'live');
      
      // 包含项目图片数据
      queryParams.append('populate', 'project_pic');
      
      const requestUrl = `/api/projects?${queryParams.toString()}`;
      const response = await api.get<StrapiProjectResponse>(requestUrl);
      
      return response.data.data || [];
    } catch (error) {
      console.error('获取科研项目列表失败:', error);
      if (error.response) {
        console.error('错误响应状态:', error.response.status);
        console.error('错误响应数据:', error.response.data);
        console.error('错误响应头:', error.response.headers);
      } else if (error.request) {
        console.error('请求错误:', error.request);
      } else {
        console.error('错误信息:', error.message);
      }
      throw error;
    }
  }

  /**
   * 根据ID获取单个科研项目
   * @param id 项目ID
   * @returns 科研项目详情
   */
  async getProjectById(id: number): Promise<Project> {
    try {
      // 由于单个资源API有权限问题，从列表中筛选指定ID的项目
      const response = await api.get<StrapiProjectResponse>(`/api/projects?populate=project_pic&filters[id][$eq]=${id}`);

      if (!response.data.data || response.data.data.length === 0) {
        throw new Error(`未找到ID为 ${id} 的项目`);
      }

      return response.data.data[0];
    } catch (error) {
      console.error(`获取科研项目详情失败 (ID: ${id}):`, error);
      throw error;
    }
  }

  /**
   * 搜索科研项目
   * @param searchTerm 搜索关键词
   * @param params 其他参数
   * @returns 搜索结果
   */
  async searchProjects(searchTerm: string, params?: PaginationParams): Promise<Project[]> {
    try {
      const searchParams = {
        ...params,
        filters: {
          ...params?.filters,
          $or: [
            { project_title: { $containsi: searchTerm } },
            { funding_agency: { $containsi: searchTerm } },
            { funding_type: { $containsi: searchTerm } }
          ]
        }
      };
      
      return this.getAllProjects(searchParams);
    } catch (error) {
      console.error('搜索科研项目失败:', error);
      throw error;
    }
  }

  /**
   * 按资助年份获取科研项目
   * @param year 年份
   * @param params 其他参数
   * @returns 指定年份的科研项目
   */
  async getProjectsByYear(year: number, params?: PaginationParams): Promise<Project[]> {
    try {
      const yearParams = {
        ...params,
        filters: {
          ...params?.filters,
          funding_year: { $eq: year }
        }
      };
      
      return this.getAllProjects(yearParams);
    } catch (error) {
      console.error(`获取${year}年科研项目失败:`, error);
      throw error;
    }
  }

  /**
   * 按资助机构获取科研项目
   * @param agency 资助机构
   * @param params 其他参数
   * @returns 指定资助机构的科研项目
   */
  async getProjectsByAgency(agency: string, params?: PaginationParams): Promise<Project[]> {
    try {
      const agencyParams = {
        ...params,
        filters: {
          ...params?.filters,
          funding_agency: { $containsi: agency }
        }
      };
      
      return this.getAllProjects(agencyParams);
    } catch (error) {
      console.error(`获取${agency}资助的科研项目失败:`, error);
      throw error;
    }
  }

  /**
   * 按资助类型获取科研项目
   * @param type 资助类型
   * @param params 其他参数
   * @returns 指定资助类型的科研项目
   */
  async getProjectsByType(type: string, params?: PaginationParams): Promise<Project[]> {
    try {
      const typeParams = {
        ...params,
        filters: {
          ...params?.filters,
          funding_type: { $containsi: type }
        }
      };
      
      return this.getAllProjects(typeParams);
    } catch (error) {
      console.error(`获取${type}类型的科研项目失败:`, error);
      throw error;
    }
  }

  /**
   * 获取所有可用的资助年份列表
   * @returns 年份列表（降序排列）
   */
  async getAvailableYears(): Promise<number[]> {
    try {
      // 获取所有科研项目，只需要年份字段
      const projects = await this.getAllProjects({
        pageSize: 1000, // 获取足够多的数据以确保包含所有年份
        sort: 'funding_year:desc'
      });
      
      // 提取年份并去重
      const years = projects
        .map(project => project.funding_year)
        .filter((year): year is number => year !== undefined && year !== null)
        .filter((year, index, array) => array.indexOf(year) === index) // 去重
        .sort((a, b) => b - a); // 降序排列
      
      return years;
    } catch (error) {
      console.error('获取可用年份列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有可用的资助机构列表
   * @returns 资助机构列表
   */
  async getAvailableAgencies(): Promise<string[]> {
    try {
      // 获取所有科研项目，只需要资助机构字段
      const projects = await this.getAllProjects({
        pageSize: 1000, // 获取足够多的数据以确保包含所有资助机构
        sort: 'funding_agency:asc'
      });
      
      // 提取资助机构并去重
      const agencies = projects
        .map(project => project.funding_agency)
        .filter((agency): agency is string => agency !== undefined && agency !== null && agency !== '')
        .filter((agency, index, array) => array.indexOf(agency) === index) // 去重
        .sort(); // 按字母顺序排列
      
      return agencies;
    } catch (error) {
      console.error('获取可用资助机构列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有可用的资助类型列表
   * @returns 资助类型列表
   */
  async getAvailableTypes(): Promise<string[]> {
    try {
      // 获取所有科研项目，只需要资助类型字段
      const projects = await this.getAllProjects({
        pageSize: 1000, // 获取足够多的数据以确保包含所有资助类型
        sort: 'funding_type:asc'
      });
      
      // 提取资助类型并去重
      const types = projects
        .map(project => project.funding_type)
        .filter((type): type is string => type !== undefined && type !== null && type !== '')
        .filter((type, index, array) => array.indexOf(type) === index) // 去重
        .sort(); // 按字母顺序排列
      
      return types;
    } catch (error) {
      console.error('获取可用资助类型列表失败:', error);
      throw error;
    }
  }
}

export default new ProjectService();

import api from './config';
import type { NewsItem, StrapiNewsResponse, PaginationParams } from '../types';

/**
 * 新闻动态 API 服务
 */
class NewsService {
  /**
   * 获取所有新闻动态
   * @param params 分页和过滤参数
   * @returns 新闻动态列表
   */
  async getAllNews(params?: PaginationParams): Promise<NewsItem[]> {
    try {
      const queryParams = new URLSearchParams();
      
      // 设置分页参数
      if (params?.page) {
        queryParams.append('pagination[page]', params.page.toString());
      }
      if (params?.pageSize) {
        queryParams.append('pagination[pageSize]', params.pageSize.toString());
      }
      
      // 设置排序参数 - 默认按日期倒序排序（最新的在前）
      if (params?.sort) {
        queryParams.append('sort', params.sort);
      } else {
        queryParams.append('sort', 'date:desc,createdAt:desc');
      }
      
      // 设置过滤参数
      if (params?.filters) {
        Object.entries(params.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            queryParams.append(`filters[${key}]`, value.toString());
          }
        });
      }
      
      // 只获取已发布的内容
      queryParams.append('publicationState', 'live');
      
      // 包含新闻封面图数据
      queryParams.append('populate', 'news_pic');
      
      const requestUrl = `/api/news?${queryParams.toString()}`;
      const response = await api.get<StrapiNewsResponse>(requestUrl);
      
      if (!response.data.data) {
        return [];
      }

      return response.data.data;
    } catch (error) {
      console.error('获取新闻动态列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取单个新闻动态
   * @param id 新闻ID
   * @returns 新闻动态详情
   */
  async getNewsById(id: number): Promise<NewsItem> {
    try {
      // 由于单个资源API有权限问题，从列表中筛选指定ID的新闻
      const response = await api.get<StrapiNewsResponse>(`/api/news?populate=news_pic&filters[id][$eq]=${id}`);

      if (!response.data.data || response.data.data.length === 0) {
        throw new Error(`未找到ID为 ${id} 的新闻`);
      }

      return response.data.data[0];
    } catch (error) {
      console.error(`获取新闻动态详情失败 (ID: ${id}):`, error);
      throw error;
    }
  }

  /**
   * 获取新闻数量（用于导航栏条件渲染）
   * @returns 新闻总数
   */
  async getNewsCount(): Promise<number> {
    try {
      const response = await api.get<StrapiNewsResponse>('/api/news?pagination[pageSize]=1&publicationState=live');
      return response.data.meta.pagination.total;
    } catch (error) {
      console.error('获取新闻数量失败:', error);
      return 0;
    }
  }

  /**
   * 获取所有可用的新闻类型列表
   * @returns 新闻类型列表
   */
  async getAvailableTypes(): Promise<string[]> {
    try {
      // 获取所有新闻动态，只需要类型字段
      const news = await this.getAllNews({
        pageSize: 1000, // 获取足够多的数据以确保包含所有类型
        sort: 'type:asc'
      });
      
      // 提取新闻类型并去重
      const types = news
        .map(item => item.type)
        .filter((type): type is string => type !== undefined && type !== null && type !== '')
        .filter((type, index, array) => array.indexOf(type) === index) // 去重
        .sort(); // 按字母顺序排列
      
      return types;
    } catch (error) {
      console.error('获取可用新闻类型列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据类型筛选新闻
   * @param type 新闻类型
   * @param params 其他分页参数
   * @returns 筛选后的新闻列表
   */
  async getNewsByType(type: string, params?: PaginationParams): Promise<NewsItem[]> {
    try {
      const filters = {
        ...params?.filters,
        type: { $eq: type }
      };

      return this.getAllNews({
        ...params,
        filters
      });
    } catch (error) {
      console.error(`根据类型筛选新闻失败 (类型: ${type}):`, error);
      throw error;
    }
  }

  /**
   * 根据日期范围筛选新闻
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @param params 其他分页参数
   * @returns 筛选后的新闻列表
   */
  async getNewsByDateRange(startDate: string, endDate: string, params?: PaginationParams): Promise<NewsItem[]> {
    try {
      const filters = {
        ...params?.filters,
        date: {
          $gte: startDate,
          $lte: endDate
        }
      };

      return this.getAllNews({
        ...params,
        filters
      });
    } catch (error) {
      console.error(`根据日期范围筛选新闻失败 (${startDate} - ${endDate}):`, error);
      throw error;
    }
  }

  /**
   * 搜索新闻（根据标题和摘要）
   * @param keyword 搜索关键词
   * @param params 其他分页参数
   * @returns 搜索结果
   */
  async searchNews(keyword: string, params?: PaginationParams): Promise<NewsItem[]> {
    try {
      const filters = {
        ...params?.filters,
        $or: [
          { title: { $containsi: keyword } },
          { abs: { $containsi: keyword } }
        ]
      };

      return this.getAllNews({
        ...params,
        filters
      });
    } catch (error) {
      console.error(`搜索新闻失败 (关键词: ${keyword}):`, error);
      throw error;
    }
  }
}

export default new NewsService();

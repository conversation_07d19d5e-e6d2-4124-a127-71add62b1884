import api from './config';
import type { StrapiContactResponse, ContactInfo } from '../types';

/**
 * 联系信息 API 服务
 */
export class ContactService {
  /**
   * 获取联系信息
   * @returns 联系信息数据
   */
  static async getContactInfo(): Promise<ContactInfo> {
    try {
      const requestUrl = '/api/contact-us';
      const response = await api.get<StrapiContactResponse>(requestUrl);

      // Strapi v5 单一类型 API 返回的是 { data: ContactInfo } 格式
      return response.data.data;
    } catch (error) {
      console.error('获取联系信息失败:', error);
      if (error.response) {
        // 如果是权限问题，返回示例数据用于演示
        if (error.response.status === 403) {
          return {
            id: 1,
            address: 'GeoCUES Lab – Geographic Computation for Urban Economy & Society',
            email: '<EMAIL>',
            lng: 116.3074, // 北京大学附近坐标
            lat: 39.9925,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            publishedAt: new Date().toISOString()
          };
        }
      }
      throw error;
    }
  }
}

export default ContactService;

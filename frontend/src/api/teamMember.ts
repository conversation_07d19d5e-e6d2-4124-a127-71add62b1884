import api from './config';
import type { StrapiTeamMemberResponse, TeamMember } from '../types';

/**
 * 团队成员 API 服务
 */
export class TeamMemberService {
  /**
   * 获取团队成员列表
   * @param params 查询参数
   * @returns 团队成员列表
   */
  static async getTeamMembers(params?: {
    role?: string;
    sort?: string;
    pagination?: {
      page?: number;
      pageSize?: number;
    };
    populate?: string;
  }): Promise<StrapiTeamMemberResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      // 添加角色筛选
      if (params?.role) {
        queryParams.append('filters[role][$eq]', params.role);
      }
      
      // 添加排序
      if (params?.sort) {
        queryParams.append('sort', params.sort);
      } else {
        // 默认按 sortOrder 升序，然后按创建时间降序
        queryParams.append('sort[0]', 'sortOrder:asc');
        queryParams.append('sort[1]', 'createdAt:desc');
      }
      
      // 添加分页
      if (params?.pagination?.page) {
        queryParams.append('pagination[page]', params.pagination.page.toString());
      }
      if (params?.pagination?.pageSize) {
        queryParams.append('pagination[pageSize]', params.pagination.pageSize.toString());
      }
      
      // 添加关联数据
      if (params?.populate) {
        queryParams.append('populate', params.populate);
      } else {
        // 默认包含头像数据（researchDirection 是 blocks 字段，自动包含）
        queryParams.append('populate', 'avatar');
      }

      const requestUrl = `/api/team-members?${queryParams.toString()}`;
      const response = await api.get(requestUrl);
      return response.data;
    } catch (error) {
      console.error('获取团队成员列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个团队成员详情
   * @param id 成员ID
   * @returns 团队成员详情
   */
  static async getTeamMember(id: number): Promise<{ data: { id: number; attributes: TeamMember } }> {
    try {
      const response = await api.get(`/api/team-members/${id}?populate=avatar`);
      return response.data;
    } catch (error) {
      console.error('获取团队成员详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建团队成员
   * @param data 成员数据
   * @returns 创建的成员信息
   */
  static async createTeamMember(data: Partial<TeamMember>): Promise<{ data: { id: number; attributes: TeamMember } }> {
    try {
      const response = await api.post('/api/team-members', { data });
      return response.data;
    } catch (error) {
      console.error('创建团队成员失败:', error);
      throw error;
    }
  }

  /**
   * 更新团队成员
   * @param id 成员ID
   * @param data 更新数据
   * @returns 更新后的成员信息
   */
  static async updateTeamMember(id: number, data: Partial<TeamMember>): Promise<{ data: { id: number; attributes: TeamMember } }> {
    try {
      const response = await api.put(`/api/team-members/${id}`, { data });
      return response.data;
    } catch (error) {
      console.error('更新团队成员失败:', error);
      throw error;
    }
  }

  /**
   * 删除团队成员
   * @param id 成员ID
   * @returns 删除结果
   */
  static async deleteTeamMember(id: number): Promise<{ data: { id: number; attributes: TeamMember } }> {
    try {
      const response = await api.delete(`/api/team-members/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除团队成员失败:', error);
      throw error;
    }
  }

  /**
   * 根据角色获取团队成员
   * @param role 角色类型
   * @returns 指定角色的团队成员列表
   */
  static async getTeamMembersByRole(role: 'Mentor' | 'Collaborator' | 'RA' | 'Alumni' | 'PhD' | 'Master' | 'Bachelor'): Promise<TeamMember[]> {
    try {
      const response = await this.getTeamMembers({
        role,
        pagination: { pageSize: 100 } // 获取足够多的数据
      });

      // Strapi v5 已经是扁平化结构，直接返回 data 数组
      return response.data;
    } catch (error) {
      console.error(`获取${role}角色成员失败:`, error);
      throw error;
    }
  }
}

export default TeamMemberService;

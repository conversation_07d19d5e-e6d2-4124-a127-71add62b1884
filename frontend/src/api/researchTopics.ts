import api from './config';
import type { StrapiResearchTopicResponse, ResearchTopic } from '../types';

/**
 * 研究主题 API 服务
 */
export class ResearchTopicsService {
  /**
   * 获取研究主题列表
   * @param params 查询参数
   * @returns 研究主题列表
   */
  static async getResearchTopics(params?: {
    sort?: string;
    pagination?: {
      page?: number;
      pageSize?: number;
    };
    populate?: string;
  }): Promise<StrapiResearchTopicResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      // 添加排序
      if (params?.sort) {
        queryParams.append('sort', params.sort);
      } else {
        // 默认按创建时间降序
        queryParams.append('sort', 'createdAt:desc');
      }
      
      // 添加分页
      if (params?.pagination?.page) {
        queryParams.append('pagination[page]', params.pagination.page.toString());
      }
      if (params?.pagination?.pageSize) {
        queryParams.append('pagination[pageSize]', params.pagination.pageSize.toString());
      } else {
        // 默认获取所有数据
        queryParams.append('pagination[pageSize]', '100');
      }
      
      // 添加关联数据
      if (params?.populate) {
        queryParams.append('populate', params.populate);
      } else {
        // 默认包含图片数据
        queryParams.append('populate', 'image');
      }

      const requestUrl = `/api/research-topics?${queryParams.toString()}`;
      const response = await api.get(requestUrl);
      return response.data;
    } catch (error) {
      console.error('获取研究主题列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取单个研究主题详情
   * @param id 主题ID
   * @returns 研究主题详情
   */
  static async getResearchTopic(id: number): Promise<{ data: ResearchTopic }> {
    try {
      const response = await api.get(`/api/research-topics/${id}?populate=image`);
      return response.data;
    } catch (error) {
      console.error('获取研究主题详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建研究主题
   * @param data 主题数据
   * @returns 创建的主题信息
   */
  static async createResearchTopic(data: Partial<ResearchTopic>): Promise<{ data: ResearchTopic }> {
    try {
      const response = await api.post('/api/research-topics', { data });
      return response.data;
    } catch (error) {
      console.error('创建研究主题失败:', error);
      throw error;
    }
  }

  /**
   * 更新研究主题
   * @param id 主题ID
   * @param data 更新数据
   * @returns 更新后的主题信息
   */
  static async updateResearchTopic(id: number, data: Partial<ResearchTopic>): Promise<{ data: ResearchTopic }> {
    try {
      const response = await api.put(`/api/research-topics/${id}`, { data });
      return response.data;
    } catch (error) {
      console.error('更新研究主题失败:', error);
      throw error;
    }
  }

  /**
   * 删除研究主题
   * @param id 主题ID
   * @returns 删除结果
   */
  static async deleteResearchTopic(id: number): Promise<{ data: ResearchTopic }> {
    try {
      const response = await api.delete(`/api/research-topics/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除研究主题失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有研究主题（简化方法）
   * @returns 研究主题数组
   */
  static async getAllResearchTopics(): Promise<ResearchTopic[]> {
    try {
      const response = await this.getResearchTopics({
        pagination: { pageSize: 100 } // 获取足够多的数据
      });

      // Strapi v5 已经是扁平化结构，直接返回 data 数组
      return response.data;
    } catch (error) {
      console.error('获取所有研究主题失败:', error);
      throw error;
    }
  }
}

export default ResearchTopicsService;

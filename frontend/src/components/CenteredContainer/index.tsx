import React from 'react';

interface CenteredContainerProps {
  children: React.ReactNode;
  maxWidth?: string;
  className?: string;
}

const CenteredContainer: React.FC<CenteredContainerProps> = ({ 
  children, 
  maxWidth = '1200px',
  className = ''
}) => {
  return (
    <div 
      className={className}
      style={{
        width: '100%',
        maxWidth: maxWidth,
        margin: '0 auto',
        padding: '0 24px'
      }}
    >
      {children}
    </div>
  );
};

export default CenteredContainer;

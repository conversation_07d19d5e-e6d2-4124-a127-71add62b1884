import React, { useRef, useMemo, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { Points, PointMaterial, Float, Sphere, MeshDistortMaterial } from '@react-three/drei';
import * as THREE from 'three';

// 获取CSS变量颜色的工具函数
const getCSSVariable = (variable: string): string => {
  if (typeof window !== 'undefined') {
    return getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
  }
  return '#007aff'; // 默认颜色
};

// 动态地球仪组件
const DynamicGlobe: React.FC = () => {
  const globeRef = useRef<THREE.Mesh>(null);
  const atmosphereRef = useRef<THREE.Mesh>(null);
  const [primaryColor, setPrimaryColor] = useState('#007aff');
  const [secondaryColor, setSecondaryColor] = useState('#34c759');

  // 监听主题变化
  useEffect(() => {
    const updateColors = () => {
      setPrimaryColor(getCSSVariable('--color-primary') || '#007aff');
      setSecondaryColor(getCSSVariable('--color-success') || '#34c759');
    };

    updateColors();

    // 监听主题变化
    const observer = new MutationObserver(updateColors);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });

    return () => observer.disconnect();
  }, []);

  // 创建地球纹理
  const globeTexture = useMemo(() => {
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 256;
    const ctx = canvas.getContext('2d')!;

    // 绘制地球轮廓
    const gradient = ctx.createLinearGradient(0, 0, 512, 256);
    gradient.addColorStop(0, primaryColor + '40');
    gradient.addColorStop(0.5, primaryColor + '20');
    gradient.addColorStop(1, primaryColor + '40');

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 512, 256);

    // 绘制大陆轮廓（简化版）
    ctx.strokeStyle = primaryColor;
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.8;

    // 绘制一些简化的大陆形状
    for (let i = 0; i < 20; i++) {
      ctx.beginPath();
      const x = Math.random() * 512;
      const y = Math.random() * 256;
      const size = 20 + Math.random() * 40;

      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.stroke();
    }

    return new THREE.CanvasTexture(canvas);
  }, [primaryColor]);

  useFrame((state) => {
    if (globeRef.current) {
      globeRef.current.rotation.y = state.clock.elapsedTime * 0.1;
      globeRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.05) * 0.1;
    }

    if (atmosphereRef.current) {
      atmosphereRef.current.rotation.y = state.clock.elapsedTime * 0.05;
      atmosphereRef.current.scale.setScalar(1 + Math.sin(state.clock.elapsedTime * 2) * 0.02);
    }
  });

  return (
    <group>
      {/* 地球主体 */}
      <Sphere ref={globeRef} args={[1.5, 64, 32]} position={[0, 0, 0]}>
        <meshPhongMaterial
          map={globeTexture}
          transparent
          opacity={0.8}
          color={primaryColor}
        />
      </Sphere>

      {/* 大气层效果 */}
      <Sphere ref={atmosphereRef} args={[1.7, 32, 16]} position={[0, 0, 0]}>
        <meshBasicMaterial
          color={secondaryColor}
          transparent
          opacity={0.1}
          side={THREE.BackSide}
        />
      </Sphere>
    </group>
  );
};

// 地理数据点组件（优化版）
const GeographicPoints: React.FC = () => {
  const ref = useRef<THREE.Points>(null);
  const [primaryColor, setPrimaryColor] = useState('#007aff');

  // 监听主题变化
  useEffect(() => {
    const updateColors = () => {
      setPrimaryColor(getCSSVariable('--color-primary') || '#007aff');
    };

    updateColors();

    const observer = new MutationObserver(updateColors);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });

    return () => observer.disconnect();
  }, []);

  // 生成地理坐标点
  const [positions, colors] = useMemo(() => {
    const positions = new Float32Array(1500 * 3);
    const colors = new Float32Array(1500 * 3);

    for (let i = 0; i < 1500; i++) {
      // 在地球表面生成点
      const lat = (Math.random() - 0.5) * Math.PI;
      const lon = (Math.random() - 0.5) * 2 * Math.PI;
      const radius = 1.8 + Math.random() * 0.3;

      positions[i * 3] = radius * Math.cos(lat) * Math.cos(lon);
      positions[i * 3 + 1] = radius * Math.sin(lat);
      positions[i * 3 + 2] = radius * Math.cos(lat) * Math.sin(lon);

      // 动态颜色
      const intensity = 0.5 + Math.random() * 0.5;
      colors[i * 3] = intensity;
      colors[i * 3 + 1] = intensity * 0.8;
      colors[i * 3 + 2] = intensity;
    }

    return [positions, colors];
  }, []);

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.05;
      ref.current.rotation.y = state.clock.elapsedTime * 0.03;
    }
  });

  return (
    <Points ref={ref} positions={positions} stride={3} frustumCulled={false}>
      <PointMaterial
        transparent
        color={primaryColor}
        size={0.015}
        sizeAttenuation={true}
        depthWrite={false}
        vertexColors
      />
    </Points>
  );
};

// 轨迹线组件（优化版）
const TrajectoryLines: React.FC = () => {
  const ref = useRef<THREE.Group>(null);
  const [successColor, setSuccessColor] = useState('#34c759');
  const [warningColor, setWarningColor] = useState('#ff9500');

  // 监听主题变化
  useEffect(() => {
    const updateColors = () => {
      setSuccessColor(getCSSVariable('--color-success') || '#34c759');
      setWarningColor(getCSSVariable('--color-warning') || '#ff9500');
    };

    updateColors();

    const observer = new MutationObserver(updateColors);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });

    return () => observer.disconnect();
  }, []);

  const trajectories = useMemo(() => {
    const lines = [];
    for (let i = 0; i < 15; i++) {
      const points = [];
      const startLat = (Math.random() - 0.5) * Math.PI * 0.6;
      const startLon = (Math.random() - 0.5) * 2 * Math.PI;

      for (let j = 0; j < 40; j++) {
        const progress = j / 39;
        const lat = startLat + (Math.random() - 0.5) * 0.3 * progress;
        const lon = startLon + progress * Math.PI * 0.4;
        const radius = 1.9 + Math.sin(progress * Math.PI) * 0.2;

        points.push(new THREE.Vector3(
          radius * Math.cos(lat) * Math.cos(lon),
          radius * Math.sin(lat),
          radius * Math.cos(lat) * Math.sin(lon)
        ));
      }
      lines.push({ points, color: i % 2 === 0 ? successColor : warningColor });
    }
    return lines;
  }, [successColor, warningColor]);

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.y = state.clock.elapsedTime * 0.015;
    }
  });

  return (
    <group ref={ref}>
      {trajectories.map((trajectory, index) => (
        <line key={index}>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={trajectory.points.length}
              array={new Float32Array(trajectory.points.flatMap(p => [p.x, p.y, p.z]))}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial color={trajectory.color} transparent opacity={0.4} />
        </line>
      ))}
    </group>
  );
};

// 浮动球体组件（优化版）
const FloatingOrbs: React.FC = () => {
  const [primaryColor, setPrimaryColor] = useState('#007aff');
  const [successColor, setSuccessColor] = useState('#34c759');
  const [warningColor, setWarningColor] = useState('#ff9500');

  // 监听主题变化
  useEffect(() => {
    const updateColors = () => {
      setPrimaryColor(getCSSVariable('--color-primary') || '#007aff');
      setSuccessColor(getCSSVariable('--color-success') || '#34c759');
      setWarningColor(getCSSVariable('--color-warning') || '#ff9500');
    };

    updateColors();

    const observer = new MutationObserver(updateColors);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });

    return () => observer.disconnect();
  }, []);

  return (
    <>
      <Float speed={1.2} rotationIntensity={0.8} floatIntensity={1.5}>
        <Sphere args={[0.25, 32, 32]} position={[-2.5, 0.8, -1.5]}>
          <MeshDistortMaterial
            color={primaryColor}
            transparent
            opacity={0.6}
            distort={0.2}
            speed={1.5}
          />
        </Sphere>
      </Float>

      <Float speed={1.8} rotationIntensity={1.2} floatIntensity={1.2}>
        <Sphere args={[0.18, 32, 32]} position={[2.2, -0.8, -1]}>
          <MeshDistortMaterial
            color={successColor}
            transparent
            opacity={0.5}
            distort={0.3}
            speed={2}
          />
        </Sphere>
      </Float>

      <Float speed={0.8} rotationIntensity={0.4} floatIntensity={2}>
        <Sphere args={[0.12, 32, 32]} position={[0, 1.5, -2.5]}>
          <MeshDistortMaterial
            color={warningColor}
            transparent
            opacity={0.4}
            distort={0.15}
            speed={1}
          />
        </Sphere>
      </Float>
    </>
  );
};

// 主背景组件
interface GeographicBackgroundProps {
  className?: string;
  style?: React.CSSProperties;
}

const GeographicBackground: React.FC<GeographicBackgroundProps> = ({
  className,
  style
}) => {
  const [primaryColor, setPrimaryColor] = useState('#007aff');
  const [successColor, setSuccessColor] = useState('#34c759');

  // 监听主题变化
  useEffect(() => {
    const updateColors = () => {
      setPrimaryColor(getCSSVariable('--color-primary') || '#007aff');
      setSuccessColor(getCSSVariable('--color-success') || '#34c759');
    };

    updateColors();

    const observer = new MutationObserver(updateColors);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });

    return () => observer.disconnect();
  }, []);

  return (
    <div
      className={`geographic-background ${className || ''}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 0,
        ...style
      }}
    >
      <Canvas
        camera={{ position: [0, 0, 6], fov: 60 }}
        style={{ background: 'transparent' }}
      >
        {/* 环境光 */}
        <ambientLight intensity={0.3} />

        {/* 主光源 */}
        <directionalLight
          position={[5, 5, 5]}
          intensity={0.8}
          color={primaryColor}
          castShadow
        />

        {/* 辅助光源 */}
        <pointLight
          position={[-5, -5, -5]}
          intensity={0.4}
          color={successColor}
        />

        {/* 背景光 */}
        <pointLight
          position={[0, 0, -10]}
          intensity={0.2}
          color="#ffffff"
        />

        {/* 地球仪 */}
        <DynamicGlobe />

        {/* 地理数据点 */}
        <GeographicPoints />

        {/* 轨迹线 */}
        <TrajectoryLines />

        {/* 浮动球体 */}
        <FloatingOrbs />
      </Canvas>
    </div>
  );
};

export default GeographicBackground;

import React from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useTheme } from '../../contexts/theme';

interface ThemeToggleProps {
  size?: 'small' | 'middle' | 'large';
  type?: 'default' | 'primary' | 'dashed' | 'link' | 'text';
  shape?: 'default' | 'circle' | 'round';
  className?: string;
  style?: React.CSSProperties;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  size = 'middle',
  type = 'text',
  shape = 'circle',
  className,
  style,
}) => {
  const { theme, toggleTheme } = useTheme();
  const isDark = theme.mode === 'dark';

  const handleToggle = () => {
    toggleTheme();
  };

  return (
    <Tooltip 
      title={isDark ? '切换到浅色模式' : '切换到暗黑模式'}
      placement="bottom"
    >
      <Button
        type={type}
        shape={shape}
        size={size}
        icon={isDark ? <SunOutlined /> : <MoonOutlined />}
        onClick={handleToggle}
        className={className}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          color: 'var(--text-primary)',
          borderColor: 'var(--border-primary)',
          ...style,
        }}
        aria-label={isDark ? '切换到浅色模式' : '切换到暗黑模式'}
      />
    </Tooltip>
  );
};

export default ThemeToggle;

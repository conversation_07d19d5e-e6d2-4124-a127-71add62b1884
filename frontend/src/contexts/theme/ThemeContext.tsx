import React, { createContext, useContext } from 'react';
import type { ThemeContextType } from '../../types';

// 创建主题上下文
export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// 自定义Hook用于使用主题上下文
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// 导出ThemeContext以供其他组件使用
export default ThemeContext;

import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * 页面切换时自动滚动到顶部的Hook
 * 提供流畅的滚动动画效果
 */
export const useScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    // 平滑滚动到页面顶部
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
    };

    // 路由切换时滚动到顶部
    scrollToTop();
  }, [location.pathname]); // 依赖路径变化

  // 返回手动滚动到顶部的函数，供组件调用
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  };

  return { scrollToTop };
};

export default useScrollToTop;

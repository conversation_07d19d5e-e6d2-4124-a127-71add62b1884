# 课题组网站前端

基于 React + TypeScript + Ant Design 构建的现代化课题组网站前端。

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 环境配置
1. 复制环境变量模板：
```bash
cp .env.example .env
```

2. 根据实际情况修改 `.env` 文件中的配置：
```bash
# API 配置
VITE_API_URL=http://localhost:1337

# 应用配置
VITE_APP_TITLE=课题组网站
VITE_APP_DESCRIPTION=人工智能课题组官方网站

# 开发环境配置
VITE_DEV_MODE=true
```

### 开发环境运行
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
src/
├── api/           # API 接口层
│   ├── config.ts  # Axios 配置
│   ├── teamMember.ts # 团队成员 API 服务
│   └── __tests__/ # API 测试文件
├── assets/        # 静态资源
├── components/    # 公共组件
├── pages/         # 页面组件
│   ├── Team/      # 团队成员页面
│   ├── Home/      # 首页
│   └── ...        # 其他页面
├── styles/        # 样式文件
├── types/         # TypeScript 类型定义
├── utils/         # 工具函数
└── main.tsx       # 应用入口
```

## 🔧 技术栈

- **框架**: React 18 + TypeScript
- **UI 库**: Ant Design 5.x
- **路由**: React Router 6
- **HTTP 客户端**: Axios
- **构建工具**: Vite
- **代码规范**: ESLint + TypeScript

## 📝 开发规范

### 组件开发
- 使用函数式组件 + Hooks
- 遵循 TypeScript 严格模式
- 组件文件使用 PascalCase 命名

### 样式规范
- 优先使用 Ant Design 组件样式
- 自定义样式使用 CSS-in-JS
- 响应式设计适配移动端

### API 调用
- 统一使用 api 目录下的服务类
- 错误处理和加载状态管理
- TypeScript 类型安全

## 🌐 环境配置

### 环境变量说明

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `VITE_API_URL` | Strapi 后端 API 基础 URL | `http://localhost:1337` |
| `VITE_APP_TITLE` | 应用标题 | `课题组网站` |
| `VITE_APP_DESCRIPTION` | 应用描述 | `人工智能课题组官方网站` |
| `VITE_DEV_MODE` | 开发模式标志 | `true` |

### 生产环境配置

生产环境需要设置正确的 API URL：

```bash
# 生产环境 .env
VITE_API_URL=https://your-api-domain.com
VITE_DEV_MODE=false
```

## 🔄 API 集成

### 团队成员页面

团队成员页面已集成 Strapi API，支持：

- ✅ 从 Strapi 获取真实数据
- ✅ 按角色分类显示（Mentor/Alumni/PhD/Master/Bachelor）
- ✅ 加载状态和错误处理
- ✅ 头像图片显示
- ✅ 响应式布局

### 使用示例

```typescript
import TeamMemberService from '@/api/teamMember';

// 获取所有团队成员
const members = await TeamMemberService.getTeamMembers();

// 按角色获取成员
const phdStudents = await TeamMemberService.getTeamMembersByRole('PhD');

// 获取单个成员详情
const member = await TeamMemberService.getTeamMember(1);
```

## 🧪 测试

### 运行测试
```bash
npm run test
```

### API 测试
API 服务包含完整的单元测试，确保数据获取和错误处理的正确性。

## 📚 相关文档

- [团队成员 API 集成文档](../docs/team-api-integration.md)
- [数据模型设计文档](../docs/schema-design.md)
- [API 规范文档](../docs/api-specs.md)

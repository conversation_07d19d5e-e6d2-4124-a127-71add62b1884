{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@react-three/drei": "^10.6.1", "@react-three/fiber": "^9.3.0", "@strapi/blocks-react-renderer": "^1.0.2", "@types/leaflet": "^1.9.20", "@types/three": "^0.178.1", "antd": "^5.26.5", "axios": "^1.11.0", "leaflet": "^1.9.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.0", "three": "^0.178.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}
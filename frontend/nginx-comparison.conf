# ========================================
# 方案对比：同服务器部署API代理配置
# ========================================

# ❌ 方案1：不使用API代理（不推荐）
# ========================================
server {
    listen 80;
    server_name geocues-lab.com;
    root /var/www/geocues-lab/dist;
    index index.html;

    # 只处理前端静态文件
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    location / {
        try_files $uri $uri/ /index.html;
    }
}

# 后端需要单独配置（如果要通过Nginx）
server {
    listen 1337;  # 暴露后端端口到外网
    server_name geocues-lab.com;
    
    location / {
        proxy_pass http://127.0.0.1:1337;
        # 后端配置...
    }
}

# 前端代码需要这样写：
# const API_BASE = 'http://geocues-lab.com:1337';
# fetch(`${API_BASE}/api/papers`);

# ========================================
# ✅ 方案2：使用API代理（推荐）
# ========================================
server {
    listen 80;
    server_name geocues-lab.com;
    root /var/www/geocues-lab/dist;
    index index.html;

    # 静态资源处理
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # API代理 - 关键配置
    location /api/ {
        # 代理到本地Strapi服务
        proxy_pass http://127.0.0.1:1337/;
        
        # 代理头设置
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # CORS头（如果需要）
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
    }

    # SPA路由处理
    location / {
        try_files $uri $uri/ /index.html;
    }
}

# 前端代码可以这样写：
# const API_BASE = '/api';  # 同域请求
# fetch(`${API_BASE}/papers`);

# ========================================
# 🔒 HTTPS版本（生产环境推荐）
# ========================================
server {
    listen 443 ssl http2;
    server_name geocues-lab.com;
    root /var/www/geocues-lab/dist;
    index index.html;

    # SSL配置
    ssl_certificate /etc/ssl/certs/geocues-lab.com.crt;
    ssl_certificate_key /etc/ssl/private/geocues-lab.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;

    # 静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # API代理 - HTTPS环境
    location /api/ {
        proxy_pass http://127.0.0.1:1337/;  # 内网仍用HTTP
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;  # 告诉后端这是HTTPS请求
        proxy_cache_bypass $http_upgrade;
    }

    # SPA路由
    location / {
        try_files $uri $uri/ /index.html;
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name geocues-lab.com;
    return 301 https://$server_name$request_uri;
}

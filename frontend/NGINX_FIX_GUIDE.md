# Nginx配置修复指南

## 🚨 问题诊断

### 根本原因
你的nginx配置中存在**location匹配优先级冲突**：

```nginx
# 问题配置：这个正则表达式规则优先级更高
location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
    expires      30d;
    error_log /dev/null;
    access_log /dev/null;
}

# 被覆盖的规则：uploads代理配置被忽略
location /uploads/ {
    proxy_pass http://127.0.0.1:1337/uploads/;
}
```

**nginx location匹配优先级**：
1. `=` 精确匹配 (最高)
2. `^~` 前缀匹配
3. `~` 正则表达式匹配 ⚠️ **这个优先级高于普通前缀**
4. `/` 普通前缀匹配 (最低)

所以图片请求被正则规则拦截，在本地文件系统查找，而不是代理到Strapi。

## 🛠️ 解决方案

### 方案1：修改现有配置（推荐）

**步骤1**: 备份当前配置
```bash
sudo cp /www/server/panel/vhost/nginx/*************.conf /www/server/panel/vhost/nginx/*************.conf.backup
```

**步骤2**: 修改静态文件规则，排除uploads目录
```nginx
# 修改前
location ~ .*\.(gif|jpg|jpeg|png|bmp|swf)$ {
    expires      30d;
    error_log /dev/null;
    access_log /dev/null;
}

# 修改后：排除uploads目录
location ~ ^/(?!uploads/).*\.(gif|jpg|jpeg|png|bmp|swf)$ {
    expires      30d;
    error_log /dev/null;
    access_log /dev/null;
    try_files $uri =404;
}
```

**步骤3**: 确保uploads和api代理配置在静态文件规则之前
```nginx
# 图片文件代理配置 - 必须放在静态文件规则之前
location /uploads/ {
    proxy_pass http://127.0.0.1:1337/uploads/;
    # ... 其他配置
}

# API 代理配置
location /api/ {
    proxy_pass http://127.0.0.1:1337;
    # ... 其他配置
}

# 静态文件规则 - 放在最后
location ~ ^/(?!uploads/).*\.(gif|jpg|jpeg|png|bmp|swf)$ {
    # ... 静态文件配置
}
```

### 方案2：使用完整的修复配置

直接使用我们提供的 `nginx-fixed.conf` 文件：

```bash
# 复制修复后的配置
sudo cp /www/wwwroot/ceus/nginx-fixed.conf /www/server/panel/vhost/nginx/*************.conf

# 测试配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx
```

## 🧪 测试验证

### 1. 运行自动化测试
```bash
cd /www/wwwroot/ceus
chmod +x test-nginx-proxy.sh
./test-nginx-proxy.sh
```

### 2. 手动测试关键路径

**测试API访问**：
```bash
curl http://*************:5000/api/projects
curl http://*************:5000/api/team-members
curl http://*************:5000/api/research-topics
curl http://*************:5000/api/news
```

**测试图片访问**：
```bash
# 先获取一个实际的图片路径
curl -s http://*************:5000/api/projects?populate=* | grep -o '"/uploads/[^"]*"' | head -1

# 然后测试图片访问（替换为实际路径）
curl -I http://*************:5000/uploads/your_actual_image.jpg
```

### 3. 浏览器测试
1. 访问 `http://*************:5000`
2. 检查各个页面：
   - 首页
   - 团队页面 (头像显示)
   - 项目页面 (项目图片)
   - 研究方向页面 (研究图片)
   - 新闻页面 (新闻图片)
3. 打开开发者工具，检查Network面板中的图片请求状态

## 🔍 故障排查

### 如果图片仍然404

**检查1**: nginx配置语法
```bash
sudo nginx -t
```

**检查2**: nginx错误日志
```bash
sudo tail -f /www/wwwlogs/*************.error.log
```

**检查3**: Strapi服务状态
```bash
# 检查Strapi是否在1337端口运行
curl http://127.0.0.1:1337/api/projects

# 检查Strapi直接图片访问
curl -I http://127.0.0.1:1337/uploads/your_image.jpg
```

**检查4**: location匹配测试
```bash
# 在nginx配置中临时添加调试信息
location /uploads/ {
    add_header X-Debug-Location "uploads-proxy" always;
    proxy_pass http://127.0.0.1:1337/uploads/;
    # ... 其他配置
}

location ~ ^/(?!uploads/).*\.(gif|jpg|jpeg|png|bmp|swf)$ {
    add_header X-Debug-Location "static-files" always;
    # ... 其他配置
}
```

然后检查响应头：
```bash
curl -I http://*************:5000/uploads/test.jpg
# 应该看到 X-Debug-Location: uploads-proxy
```

## 📋 配置检查清单

- [ ] uploads代理配置在静态文件规则之前
- [ ] 静态文件规则排除uploads目录
- [ ] nginx配置语法正确 (`nginx -t`)
- [ ] nginx服务重新加载
- [ ] Strapi在1337端口正常运行
- [ ] API请求正常返回数据
- [ ] 图片代理请求返回200状态码
- [ ] 浏览器中图片正常显示

## 🎯 最终验证

所有测试通过后，你应该能看到：
- ✅ API数据正常加载
- ✅ 团队成员头像正常显示
- ✅ 项目图片正常显示
- ✅ 研究方向图片正常显示
- ✅ 新闻图片正常显示

如果仍有问题，请提供：
1. nginx错误日志
2. 浏览器开发者工具中的Network面板截图
3. 具体的图片URL和响应状态

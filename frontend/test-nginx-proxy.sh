#!/bin/bash

# nginx代理测试脚本
# 用于验证图片访问问题修复

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 服务器配置
SERVER_IP="*************"
FRONTEND_PORT="5000"
BACKEND_PORT="1337"

echo -e "${BLUE}🧪 nginx代理测试开始...${NC}"
echo ""

# 1. 测试直接访问Strapi
echo -e "${YELLOW}1. 测试直接访问Strapi后端...${NC}"
echo "测试URL: http://${SERVER_IP}:${BACKEND_PORT}/api/projects"

if curl -s -f "http://${SERVER_IP}:${BACKEND_PORT}/api/projects" > /dev/null; then
    echo -e "${GREEN}✅ Strapi直接访问正常${NC}"
else
    echo -e "${RED}❌ Strapi直接访问失败${NC}"
    echo "请检查Strapi是否在${BACKEND_PORT}端口正常运行"
    exit 1
fi
echo ""

# 2. 测试通过nginx代理访问API
echo -e "${YELLOW}2. 测试通过nginx代理访问API...${NC}"
echo "测试URL: http://${SERVER_IP}:${FRONTEND_PORT}/api/projects"

if curl -s -f "http://${SERVER_IP}:${FRONTEND_PORT}/api/projects" > /dev/null; then
    echo -e "${GREEN}✅ nginx API代理正常${NC}"
else
    echo -e "${RED}❌ nginx API代理失败${NC}"
    echo "请检查nginx配置和服务状态"
    exit 1
fi
echo ""

# 3. 测试图片访问
echo -e "${YELLOW}3. 测试图片访问...${NC}"

# 测试多个API端点的图片
echo "测试各个页面的图片数据..."

# 测试项目图片
echo ""
echo -e "${BLUE}3.1 测试项目页面图片:${NC}"
PROJECT_DATA=$(curl -s "http://${SERVER_IP}:${FRONTEND_PORT}/api/projects?populate=*" | head -c 2000)
if echo "$PROJECT_DATA" | grep -q "uploads"; then
    IMAGE_PATH=$(echo "$PROJECT_DATA" | grep -o '"/uploads/[^"]*"' | head -1 | tr -d '"')
    if [ -n "$IMAGE_PATH" ]; then
        echo "项目图片路径: $IMAGE_PATH"
        PROXY_URL="http://${SERVER_IP}:${FRONTEND_PORT}${IMAGE_PATH}"
        echo "代理访问URL: $PROXY_URL"
        if curl -s -I "$PROXY_URL" | grep -q "200 OK"; then
            echo -e "${GREEN}✅ 项目图片代理正常${NC}"
        else
            echo -e "${RED}❌ 项目图片代理失败${NC}"
        fi
    fi
fi

# 测试团队成员头像
echo ""
echo -e "${BLUE}3.2 测试团队成员头像:${NC}"
TEAM_DATA=$(curl -s "http://${SERVER_IP}:${FRONTEND_PORT}/api/team-members?populate=*" | head -c 2000)
if echo "$TEAM_DATA" | grep -q "uploads"; then
    AVATAR_PATH=$(echo "$TEAM_DATA" | grep -o '"/uploads/[^"]*"' | head -1 | tr -d '"')
    if [ -n "$AVATAR_PATH" ]; then
        echo "头像路径: $AVATAR_PATH"
        PROXY_URL="http://${SERVER_IP}:${FRONTEND_PORT}${AVATAR_PATH}"
        echo "代理访问URL: $PROXY_URL"
        if curl -s -I "$PROXY_URL" | grep -q "200 OK"; then
            echo -e "${GREEN}✅ 团队头像代理正常${NC}"
        else
            echo -e "${RED}❌ 团队头像代理失败${NC}"
        fi
    fi
fi

# 测试研究方向图片
echo ""
echo -e "${BLUE}3.3 测试研究方向图片:${NC}"
RESEARCH_DATA=$(curl -s "http://${SERVER_IP}:${FRONTEND_PORT}/api/research-topics?populate=*" | head -c 2000)
if echo "$RESEARCH_DATA" | grep -q "uploads"; then
    RESEARCH_PATH=$(echo "$RESEARCH_DATA" | grep -o '"/uploads/[^"]*"' | head -1 | tr -d '"')
    if [ -n "$RESEARCH_PATH" ]; then
        echo "研究图片路径: $RESEARCH_PATH"
        PROXY_URL="http://${SERVER_IP}:${FRONTEND_PORT}${RESEARCH_PATH}"
        echo "代理访问URL: $PROXY_URL"
        if curl -s -I "$PROXY_URL" | grep -q "200 OK"; then
            echo -e "${GREEN}✅ 研究图片代理正常${NC}"
        else
            echo -e "${RED}❌ 研究图片代理失败${NC}"
        fi
    fi
fi

# 测试新闻图片
echo ""
echo -e "${BLUE}3.4 测试新闻图片:${NC}"
NEWS_DATA=$(curl -s "http://${SERVER_IP}:${FRONTEND_PORT}/api/news?populate=*" | head -c 2000)
if echo "$NEWS_DATA" | grep -q "uploads"; then
    NEWS_PATH=$(echo "$NEWS_DATA" | grep -o '"/uploads/[^"]*"' | head -1 | tr -d '"')
    if [ -n "$NEWS_PATH" ]; then
        echo "新闻图片路径: $NEWS_PATH"
        PROXY_URL="http://${SERVER_IP}:${FRONTEND_PORT}${NEWS_PATH}"
        echo "代理访问URL: $PROXY_URL"
        if curl -s -I "$PROXY_URL" | grep -q "200 OK"; then
            echo -e "${GREEN}✅ 新闻图片代理正常${NC}"
        else
            echo -e "${RED}❌ 新闻图片代理失败${NC}"
        fi
    fi
fi
echo ""

# 4. 测试特定图片（如果提供）
if [ -n "$1" ]; then
    echo -e "${YELLOW}4. 测试指定图片: $1${NC}"
    
    SPECIFIC_DIRECT="http://${SERVER_IP}:${BACKEND_PORT}/api/uploads/$1"
    SPECIFIC_PROXY="http://${SERVER_IP}:${FRONTEND_PORT}/api/uploads/$1"
    
    echo "直接访问: $SPECIFIC_DIRECT"
    if curl -s -I "$SPECIFIC_DIRECT" | grep -q "200 OK"; then
        echo -e "${GREEN}✅ 直接访问成功${NC}"
    else
        echo -e "${RED}❌ 直接访问失败${NC}"
    fi
    
    echo "代理访问: $SPECIFIC_PROXY"
    if curl -s -I "$SPECIFIC_PROXY" | grep -q "200 OK"; then
        echo -e "${GREEN}✅ 代理访问成功${NC}"
    else
        echo -e "${RED}❌ 代理访问失败${NC}"
    fi
    echo ""
fi

# 5. 显示nginx日志
echo -e "${YELLOW}5. 检查nginx日志...${NC}"
echo "最近的nginx错误日志:"
sudo tail -5 /var/log/nginx/error.log 2>/dev/null || echo "无法读取nginx错误日志"
echo ""

echo "最近的nginx访问日志:"
sudo tail -5 /var/log/nginx/geocues-lab-ip.access.log 2>/dev/null || echo "无法读取nginx访问日志"
echo ""

# 6. 总结
echo -e "${BLUE}🎉 测试完成！${NC}"
echo ""
echo -e "${YELLOW}使用方法:${NC}"
echo "  基本测试: ./test-nginx-proxy.sh"
echo "  测试特定图片: ./test-nginx-proxy.sh cr_49c991420d.png"
echo ""
echo -e "${YELLOW}故障排查:${NC}"
echo "  1. 检查nginx配置: sudo nginx -t"
echo "  2. 重启nginx: sudo systemctl restart nginx"
echo "  3. 检查Strapi状态: curl http://localhost:${BACKEND_PORT}/api/projects"
echo "  4. 查看详细日志: sudo tail -f /var/log/nginx/error.log"

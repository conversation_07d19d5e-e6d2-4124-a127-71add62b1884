# Nginx 配置 - 使用服务器IP地址
# 适用于没有域名的情况

server {
    listen 5000;  # 前端服务运行在5000端口
    server_name *************;  # 使用真实服务器IP地址
    root /var/www/geocues-lab/dist;  # 修改为你的实际部署路径
    index index.html;

    # 访问日志
    access_log /var/log/nginx/geocues-lab-ip.access.log;
    error_log /var/log/nginx/geocues-lab-ip.error.log;

    # 启用 gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        try_files $uri =404;
    }

    # 图片文件代理配置
    location /uploads/ {
        # 直接转发到Strapi的uploads目录
        proxy_pass http://127.0.0.1:1337/uploads/;

        # 基本代理头
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 图片缓存设置
        expires 1y;
        add_header Cache-Control "public, immutable";

        # CORS设置
        add_header Access-Control-Allow-Origin "http://*************:5000";
    }

    # API 代理配置 - 统一处理所有API请求
    location /api/ {
        # 关键：保持完整路径，直接转发到Strapi（包含/api前缀）
        proxy_pass http://127.0.0.1:1337;

        # 基本代理头
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # CORS 头设置（重要：IP访问时可能需要）
        add_header Access-Control-Allow-Origin "http://*************:5000";
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
        add_header Access-Control-Allow-Credentials true;

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "http://*************:5000";
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type 'text/plain; charset=utf-8';
            add_header Content-Length 0;
            return 204;
        }
    }

    # SPA 路由回退配置
    location / {
        try_files $uri $uri/ /index.html;
        
        # HTML 文件不缓存
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # 错误页面处理
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# 如果需要支持其他IP访问（如内网IP）
server {
    listen 5000;
    server_name _;  # 匹配所有未明确配置的域名/IP
    root /var/www/geocues-lab/dist;
    index index.html;

    # 重定向到主IP（可选）
    return 301 http://*************:5000$request_uri;
}

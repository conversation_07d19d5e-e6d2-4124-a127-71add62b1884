#!/bin/bash

# 部署脚本 - 使用服务器IP地址
# 使用方法: ./deploy-ip.sh

set -e  # 遇到错误立即退出

# 配置变量
SERVER_IP="*************"
DEPLOY_PATH="/var/www/geocues-lab"
NGINX_CONFIG_NAME="geocues-lab-ip"

echo "🚀 开始部署到服务器 IP: $SERVER_IP"

# 1. 构建前端项目
echo "📦 构建前端项目..."
npm run build

# 2. 检查构建结果
if [ ! -d "dist" ]; then
    echo "❌ 构建失败，dist目录不存在"
    exit 1
fi

echo "✅ 前端构建完成"

# 3. 部署到服务器（需要SSH访问权限）
echo "📤 部署文件到服务器..."

# 创建部署目录
ssh root@$SERVER_IP "mkdir -p $DEPLOY_PATH"

# 上传构建文件
rsync -avz --delete dist/ root@$SERVER_IP:$DEPLOY_PATH/dist/

# 上传Nginx配置
scp nginx-ip.conf root@$SERVER_IP:/etc/nginx/sites-available/$NGINX_CONFIG_NAME

echo "⚙️  配置Nginx..."

# 启用站点配置
ssh root@$SERVER_IP "ln -sf /etc/nginx/sites-available/$NGINX_CONFIG_NAME /etc/nginx/sites-enabled/"

# 测试Nginx配置
ssh root@$SERVER_IP "nginx -t"

# 重启Nginx
ssh root@$SERVER_IP "systemctl reload nginx"

echo "✅ 部署完成！"
echo "🌐 访问地址: http://$SERVER_IP"
echo "📋 测试路由:"
echo "   - http://$SERVER_IP/contact"
echo "   - http://$SERVER_IP/team"
echo "   - http://$SERVER_IP/research"
echo "   - http://$SERVER_IP/api/papers"

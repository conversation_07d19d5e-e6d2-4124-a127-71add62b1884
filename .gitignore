# ============================
# 操作系统相关文件
# ============================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
.Spotlight-V100
.Trashes
._*

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp

# Linux
*~

# ============================
# Node.js 相关
# ============================

# 依赖包
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 包管理器
.yarn/*
!.yarn/cache
!.yarn/unplugged
!.yarn/patches
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*
yarn-error.log

# ============================
# 构建输出
# ============================

# 前端构建
frontend/dist/
frontend/dist-ssr/
frontend/build/

# 后端构建
backend/dist/
backend/build/

# TypeScript 构建信息
*.tsbuildinfo
.eslintcache

# ============================
# 环境变量和配置
# ============================

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Strapi 配置
.strapi/
.strapi-updater.json
.strapi-cloud.json

# ============================
# 数据库
# ============================

# SQLite 数据库
*.sqlite
*.sqlite3
*.db

# 数据库备份
*.sql

# ============================
# 日志文件
# ============================

logs/
*.log
.tmp/

# ============================
# IDE 和编辑器
# ============================

# VSCode
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json

# IntelliJ IDEA
.idea/
*.iml
*.iws
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*.swn
*.swm

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================
# 测试和覆盖率
# ============================

coverage/
.nyc_output/
.coverage
*.cover
.hypothesis/
.pytest_cache/

# ============================
# Docker 相关
# ============================

# Docker 临时文件
.dockerignore

# ============================
# 上传和临时文件
# ============================

# Strapi 上传文件
public/uploads/*
!public/uploads/.gitkeep

# 临时文件
*.tmp
*.temp
.cache/

# ============================
# 压缩包和二进制文件
# ============================

*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
*.com
*.class
*.dll
*.exe
*.o
*.so
*.out
*.pid

# ============================
# 其他
# ============================

# 许可证文件
license.txt

# 导出文件
exports/

# 备份文件
*.bak
*.backup

# 系统文件
.fuse_hidden*
.nfs*

# 锁文件
package-lock.json.bak
yarn.lock.bak

# API接口文档

## 📋 概述

本文档定义了课题组网站的前后端API接口规范，基于Strapi CMS框架构建。

### 基础信息
- **基础URL**: `http://localhost:1337`
- **API版本**: v1
- **认证方式**: JWT Token
- **数据格式**: JSON

## 🔐 认证

### 获取Token
```http
POST /api/auth/local
Content-Type: application/json

{
  "identifier": "<EMAIL>",
  "password": "password"
}
```

**响应示例**:
```json
{
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "role": {
      "id": 1,
      "name": "Authenticated",
      "description": "Default role given to authenticated user."
    }
  }
}
```

### 使用Token
```http
GET /api/team-members
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 👥 课题组成员 API

### 获取成员列表
```http
GET /api/team-members
```

**查询参数**:
- `pagination[page]`: 页码 (默认: 1)
- `pagination[pageSize]`: 每页数量 (默认: 25)
- `filters[role][$eq]`: 角色筛选 (导师/博士生/硕士生/本科生/访问学者)
- `sort`: 排序字段 (name:asc, createdAt:desc)

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "attributes": {
        "name": "李教授",
        "title": "教授、博士生导师",
        "role": "导师",
        "researchDirection": "人工智能、机器学习",
        "email": "<EMAIL>",
        "phone": "010-12345678",
        "bio": "李教授是人工智能领域的知名专家...",
        "education": "清华大学计算机系博士",
        "avatar": {
          "data": {
            "id": 1,
            "attributes": {
              "url": "/uploads/avatar_1.jpg",
              "width": 300,
              "height": 300
            }
          }
        },
        "createdAt": "2024-12-19T08:00:00.000Z",
        "updatedAt": "2024-12-19T08:00:00.000Z"
      }
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "pageSize": 25,
      "pageCount": 1,
      "total": 1
    }
  }
}
```

### 获取单个成员
```http
GET /api/team-members/{id}
```

### 创建成员
```http
POST /api/team-members
Content-Type: application/json
Authorization: Bearer {token}

{
  "data": {
    "name": "张三",
    "title": "博士研究生",
    "role": "博士生",
    "researchDirection": "计算机视觉、目标检测",
    "email": "<EMAIL>",
    "bio": "专注于深度学习在计算机视觉中的应用研究。",
    "education": "北京大学计算机系硕士"
  }
}
```

### 更新成员
```http
PUT /api/team-members/{id}
Content-Type: application/json
Authorization: Bearer {token}

{
  "data": {
    "name": "张三",
    "title": "博士研究生",
    "researchDirection": "计算机视觉、深度学习"
  }
}
```

### 删除成员
```http
DELETE /api/team-members/{id}
Authorization: Bearer {token}
```

## 🔬 研究方向 API

### 获取研究方向列表
```http
GET /api/research-directions
```

**查询参数**:
- `pagination[page]`: 页码
- `pagination[pageSize]`: 每页数量
- `populate`: 关联数据 (image, keywords)

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "attributes": {
        "title": "计算机视觉",
        "description": "计算机视觉是人工智能的重要分支...",
        "keywords": ["图像识别", "目标检测", "视频分析"],
        "image": {
          "data": {
            "id": 1,
            "attributes": {
              "url": "/uploads/computer_vision.jpg"
            }
          }
        },
        "applications": [
          "智能监控系统",
          "自动驾驶技术",
          "医疗影像诊断"
        ],
        "achievements": [
          "在ImageNet竞赛中取得优异成绩",
          "发表多篇CVPR、ICCV顶级会议论文"
        ]
      }
    }
  ]
}
```

## 📊 科研项目 API

### 获取项目列表
```http
GET /api/research-projects
```

**查询参数**:
- `filters[status][$eq]`: 项目状态 (进行中/已完成/计划中)
- `filters[category][$eq]`: 项目类别 (国家级/省部级/企业合作)
- `sort`: 排序 (startDate:desc, createdAt:desc)

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "attributes": {
        "title": "基于深度学习的医学图像智能诊断系统",
        "description": "本项目旨在开发一套基于深度学习的医学图像智能诊断系统...",
        "leader": "李教授",
        "members": ["张三", "王五", "赵六"],
        "funding": "国家自然科学基金",
        "budget": 200,
        "startDate": "2024-01-01",
        "endDate": "2027-12-31",
        "status": "进行中",
        "category": "国家级",
        "progress": 35,
        "keywords": ["深度学习", "医学图像", "智能诊断"],
        "achievements": [
          "完成了数据预处理流程的设计和实现",
          "开发了基于CNN的病变检测算法"
        ]
      }
    }
  ]
}
```

## 📚 学术成果 API

### 获取成果列表
```http
GET /api/academic-achievements
```

**查询参数**:
- `filters[type][$eq]`: 成果类型 (论文/专利/获奖/报告)
- `filters[publicationDate][$gte]`: 开始日期
- `filters[publicationDate][$lte]`: 结束日期
- `sort`: 排序 (publicationDate:desc, impactFactor:desc)

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "attributes": {
        "title": "Deep Learning for Medical Image Analysis: A Comprehensive Survey",
        "type": "论文",
        "authors": ["张三", "李教授", "王五"],
        "journal": "IEEE Transactions on Medical Imaging",
        "publicationDate": "2024-06-15",
        "doi": "10.1109/TMI.2024.123456",
        "impactFactor": 8.5,
        "citations": 25,
        "keywords": ["深度学习", "医学图像", "计算机视觉"],
        "abstract": "本文全面综述了深度学习在医学图像分析中的应用..."
      }
    }
  ]
}
```

## 📰 新闻动态 API

### 获取新闻列表
```http
GET /api/news
```

**查询参数**:
- `filters[category][$eq]`: 新闻类别 (项目获批/学术成果/学术交流)
- `filters[publishedAt][$gte]`: 开始日期
- `filters[publishedAt][$lte]`: 结束日期
- `sort`: 排序 (publishedAt:desc)

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "attributes": {
        "title": "课题组获得国家自然科学基金项目",
        "summary": "我们课题组成功申请到国家自然科学基金面上项目...",
        "content": "近日，我们课题组成功申请到国家自然科学基金面上项目...",
        "category": "项目获批",
        "tags": ["国家自然科学基金", "深度学习", "医学图像"],
        "author": "李教授",
        "publishedAt": "2024-12-15T00:00:00.000Z",
        "cover": {
          "data": {
            "id": 1,
            "attributes": {
              "url": "/uploads/news_cover_1.jpg"
            }
          }
        },
        "views": 156
      }
    }
  ]
}
```

## 📁 文件上传 API

### 上传文件
```http
POST /api/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}

file: [文件数据]
```

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "attributes": {
        "name": "avatar.jpg",
        "alternativeText": null,
        "caption": null,
        "width": 300,
        "height": 300,
        "formats": {
          "thumbnail": {
            "name": "thumbnail_avatar.jpg",
            "hash": "thumbnail_avatar_123",
            "ext": ".jpg",
            "mime": "image/jpeg",
            "width": 156,
            "height": 156,
            "size": 5.94,
            "url": "/uploads/thumbnail_avatar_123.jpg"
          }
        },
        "hash": "avatar_123",
        "ext": ".jpg",
        "mime": "image/jpeg",
        "size": 25.1,
        "url": "/uploads/avatar_123.jpg",
        "previewUrl": null,
        "provider": "local",
        "provider_metadata": null,
        "createdAt": "2024-12-19T08:00:00.000Z",
        "updatedAt": "2024-12-19T08:00:00.000Z"
      }
    }
  ]
}
```

## 🔍 搜索 API

### 全局搜索
```http
GET /api/search
```

**查询参数**:
- `q`: 搜索关键词
- `type`: 搜索类型 (team-members, projects, publications, news)
- `limit`: 结果数量限制

**响应示例**:
```json
{
  "data": {
    "team-members": [
      {
        "id": 1,
        "type": "team-member",
        "title": "李教授",
        "description": "人工智能、机器学习",
        "url": "/team"
      }
    ],
    "projects": [
      {
        "id": 1,
        "type": "project",
        "title": "基于深度学习的医学图像智能诊断系统",
        "description": "本项目旨在开发一套基于深度学习的医学图像智能诊断系统...",
        "url": "/projects"
      }
    ]
  }
}
```

## 📊 统计 API

### 获取网站统计
```http
GET /api/statistics
```

**响应示例**:
```json
{
  "data": {
    "teamMembers": {
      "total": 15,
      "byRole": {
        "导师": 1,
        "博士生": 3,
        "硕士生": 5,
        "本科生": 4,
        "访问学者": 2
      }
    },
    "projects": {
      "total": 8,
      "byStatus": {
        "进行中": 3,
        "已完成": 4,
        "计划中": 1
      },
      "byCategory": {
        "国家级": 2,
        "省部级": 3,
        "企业合作": 2,
        "其他": 1
      }
    },
    "publications": {
      "total": 50,
      "byType": {
        "论文": 30,
        "专利": 10,
        "获奖": 5,
        "报告": 5
      }
    }
  }
}
```

## ⚠️ 错误处理

### 错误响应格式
```json
{
  "error": {
    "status": 400,
    "name": "BadRequestError",
    "message": "Invalid request",
    "details": {
      "errors": [
        {
          "path": ["name"],
          "message": "Name is required"
        }
      ]
    }
  }
}
```

### 常见错误码
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

## 🔧 前端集成示例

### 使用Axios调用API
```javascript
import api from '@/api/config';

// 获取团队成员列表
const getTeamMembers = async (params = {}) => {
  try {
    const response = await api.get('/api/team-members', { params });
    return response.data;
  } catch (error) {
    console.error('获取团队成员失败:', error);
    throw error;
  }
};

// 创建团队成员
const createTeamMember = async (data) => {
  try {
    const response = await api.post('/api/team-members', { data });
    return response.data;
  } catch (error) {
    console.error('创建团队成员失败:', error);
    throw error;
  }
};

// 上传文件
const uploadFile = async (file) => {
  try {
    const formData = new FormData();
    formData.append('files', file);
    
    const response = await api.post('/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('文件上传失败:', error);
    throw error;
  }
};
```

## 📝 注意事项

1. **分页**: 所有列表接口都支持分页，默认每页25条记录
2. **关联数据**: 使用`populate`参数获取关联数据
3. **过滤**: 使用`filters`参数进行数据过滤
4. **排序**: 使用`sort`参数进行数据排序
5. **认证**: 需要认证的接口必须在请求头中包含JWT Token
6. **文件上传**: 文件上传接口支持多种格式，建议限制文件大小
7. **错误处理**: 所有接口都应该进行适当的错误处理 
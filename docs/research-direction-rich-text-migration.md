# 研究方向字段 Rich Text 迁移

## 修改概述

将团队成员页面（`frontend/src/pages/Team/index.tsx`）中的 `researchDirection` 字段从普通文本类型迁移为 Strapi rich text (blocks) 类型，以支持更丰富的内容格式和更好的编辑体验。

## 修改详情

### 1. API 请求代码更新

**修改文件**: `frontend/src/api/teamMember.ts`

**修改位置**: 第 50-54 行和第 71 行

**修改内容**:

```typescript
// 修改前
queryParams.append('populate', 'avatar');

// 修改后
queryParams.append('populate[avatar]', '*');
queryParams.append('populate[researchDirection]', '*');
```

```typescript
// 修改前
const response = await api.get(`/api/team-members/${id}?populate=avatar`);

// 修改后
const response = await api.get(`/api/team-members/${id}?populate[avatar]=*&populate[researchDirection]=*`);
```

**目的**: 确保 API 请求包含 `researchDirection` 字段的 blocks 数据，使用 Strapi v5 的 populate 语法。

### 2. TypeScript 类型定义更新

**修改文件**: `frontend/src/types/index.ts`

**修改位置**: 第 69 行

**修改内容**:

```typescript
// 修改前
researchDirection: string;

// 修改后
researchDirection: StrapiBlocks;
```

**目的**: 将 `TeamMember` 接口中的 `researchDirection` 字段类型从 `string` 改为 `StrapiBlocks`，确保类型安全。

### 3. StrapiBlocksContent 组件增强

**修改文件**: `frontend/src/pages/Team/index.tsx`

**修改位置**: 第 73-109 行

**增强功能**:

```typescript
const StrapiBlocksContent: React.FC<{ 
  content: StrapiBlocks | undefined;
  style?: React.CSSProperties;
  compact?: boolean;
}> = ({ content, style, compact = false }) => {
  // 组件实现
};
```

**新增特性**:
- **style 属性**: 支持自定义样式覆盖
- **compact 模式**: 为卡片中的紧凑显示优化
- **动态样式**: 根据 compact 模式调整字体大小、颜色和间距

**样式配置**:
- **普通模式**: 15px 字体，#1d1d1f 颜色，12px 段落间距
- **紧凑模式**: 13px 字体，#86868b 颜色，6px 段落间距

### 4. 研究方向渲染更新

#### 4.1 团队成员卡片渲染

**修改位置**: 第 386-400 行

**修改前**:
```typescript
<Paragraph style={{
  margin: 0,
  fontSize: '13px',
  color: '#86868b',
  lineHeight: '1.5',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: heightDist.textLines,
  WebkitBoxOrient: 'vertical'
}}>
  {member.researchDirection}
</Paragraph>
```

**修改后**:
```typescript
<StrapiBlocksContent 
  content={member.researchDirection} 
  compact={true}
  style={{
    fontSize: '13px',
    color: '#86868b',
    lineHeight: '1.5'
  }}
/>
```

#### 4.2 Mentor 详情弹窗渲染

**修改位置**: 第 711-721 行

**修改前**:
```typescript
<Paragraph style={{
  margin: 0,
  fontSize: '15px',
  color: '#1d1d1f',
  lineHeight: '1.6',
  background: '#f8f9fa',
  padding: '16px',
  borderRadius: '12px'
}}>
  {selectedMentor.researchDirection}
</Paragraph>
```

**修改后**:
```typescript
<div style={{
  margin: 0,
  fontSize: '15px',
  color: '#1d1d1f',
  lineHeight: '1.6',
  background: '#f8f9fa',
  padding: '16px',
  borderRadius: '12px'
}}>
  <StrapiBlocksContent content={selectedMentor.researchDirection} />
</div>
```

### 5. 高度控制策略调整

**原有问题**: `WebkitLineClamp` 对 rich text 内容无效

**解决方案**: 使用 `maxHeight` 和 `overflow: hidden` 控制区域高度

**实现方式**:
```typescript
<div style={{
  flex: '1 1 auto',
  overflow: 'hidden',
  maxHeight: '100%'  // 确保内容不超出分配区域
}}>
```

**优势**:
- 适用于任何类型的内容（文本、列表、标题等）
- 保持各角色卡片的布局一致性
- 自动处理内容溢出

## 技术实现细节

### 1. Strapi Blocks 数据结构

**API 返回格式**:
```json
{
  "researchDirection": [
    {
      "type": "paragraph",
      "children": [
        {
          "text": "研究内容文本",
          "bold": false,
          "italic": false
        }
      ]
    },
    {
      "type": "heading",
      "level": 2,
      "children": [
        {
          "text": "研究方向标题"
        }
      ]
    }
  ]
}
```

### 2. BlocksRenderer 自定义样式

**段落样式**:
```typescript
paragraph: ({ children }) => (
  <p style={{ 
    margin: compact ? '0 0 6px 0' : '0 0 12px 0', 
    lineHeight: '1.5',
    fontSize: 'inherit',
    color: 'inherit'
  }}>
    {children}
  </p>
)
```

**标题样式**:
```typescript
heading: ({ children, level }) => {
  const headingStyle = {
    margin: '16px 0 8px 0',
    color: '#1d1d1f',
    fontWeight: 600
  };
  // 根据 level 返回对应的标题元素
}
```

### 3. 错误处理机制

**空内容处理**:
```typescript
if (!content || !Array.isArray(content)) {
  return <span style={{ color: '#86868b', fontStyle: 'italic' }}>暂无信息</span>;
}
```

**类型转换**:
```typescript
const blocksContent = content as unknown as BlocksContent;
```

## 兼容性考虑

### 1. 向后兼容

- 保持原有的 API 接口不变
- 支持空内容的优雅降级
- 维持现有的视觉样式和布局

### 2. 数据迁移

**后端要求**:
- Strapi 后端需要将 `researchDirection` 字段类型从 Text 改为 Blocks
- 现有数据需要迁移为 blocks 格式

**前端适配**:
- 自动处理新旧数据格式
- 提供回退显示方案

### 3. 性能优化

**渲染优化**:
- 使用 React.FC 类型确保组件性能
- 避免不必要的重新渲染
- 合理的样式继承机制

## 用户体验改进

### 1. 内容展示

**支持的格式**:
- ✅ 段落文本
- ✅ 标题（H1-H6）
- ✅ 有序/无序列表
- ✅ 引用块
- ✅ 代码块
- ✅ 文本格式（粗体、斜体、下划线等）

### 2. 编辑体验

**Strapi 后台优势**:
- 可视化富文本编辑器
- 实时预览功能
- 结构化内容管理
- 更好的内容组织

### 3. 显示效果

**卡片中的紧凑显示**:
- 较小的字体和间距
- 适应有限的显示空间
- 保持内容的可读性

**详情弹窗中的完整显示**:
- 标准的字体和间距
- 完整的格式支持
- 更好的阅读体验

## 测试验证

### 1. 功能测试

- ✅ API 请求包含正确的 populate 参数
- ✅ TypeScript 类型检查通过
- ✅ Rich text 内容正确渲染
- ✅ 空内容优雅降级
- ✅ 不同角色的布局保持一致

### 2. 视觉测试

- ✅ 卡片中的紧凑显示效果
- ✅ 弹窗中的完整显示效果
- ✅ 各种 rich text 格式的渲染
- ✅ 响应式布局兼容性

### 3. 性能测试

- ✅ 组件渲染性能正常
- ✅ 热更新功能正常
- ✅ 无内存泄漏问题

## 后续优化建议

### 1. 内容长度控制

考虑为不同角色设置不同的内容长度限制：
- Alumni: 简短描述（1-2 段）
- 学生: 中等长度（2-3 段）
- Mentor: 详细描述（无限制）

### 2. 搜索功能增强

Rich text 内容可以支持更好的搜索功能：
- 全文搜索
- 标签搜索
- 结构化内容搜索

### 3. SEO 优化

Rich text 内容有利于 SEO：
- 更好的内容结构
- 语义化标签
- 搜索引擎友好

## 总结

本次迁移成功实现了以下目标：

1. ✅ **API 集成**: 正确获取 Strapi blocks 数据
2. ✅ **类型安全**: 更新 TypeScript 类型定义
3. ✅ **渲染升级**: 使用 BlocksRenderer 替代纯文本
4. ✅ **样式适配**: 支持紧凑和完整两种显示模式
5. ✅ **布局保持**: 维持各角色卡片的一致性
6. ✅ **错误处理**: 提供优雅的降级方案

迁移后的系统支持更丰富的内容格式，提供更好的编辑和显示体验，同时保持了原有的功能和视觉效果。

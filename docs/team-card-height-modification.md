# 团队成员卡片高度修改

## 修改概述

根据用户要求，对团队成员页面（`frontend/src/pages/Team/index.tsx`）中 Mentor 角色成员的卡片进行了高度固定设置。

## 修改详情

### 1. 卡片整体高度设置

**修改位置**: 第 220-234 行，Card 组件的 style 属性

**修改内容**:
- 将 Mentor 角色成员卡片的高度固定为 `512px`
- 其他角色成员卡片保持原有的 `420px` 高度
- 同时设置了 `minHeight` 确保高度一致性

```typescript
style={{
  borderRadius: '20px',
  border: 'none',
  background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
  height: member.role === 'Mentor' ? '512px' : '420px',
  minHeight: member.role === 'Mentor' ? '512px' : '420px',
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
  cursor: member.role === 'Mentor' ? 'pointer' : 'default'
}}
```

### 2. 研究方向区域高度设置

**修改位置**: 第 320-359 行，研究方向部分

**修改内容**:
- 为 Mentor 角色成员的研究方向区域设置固定高度 `100px`
- 其他角色成员保持自适应高度
- 添加了文本溢出处理，使用 CSS 多行省略号

```typescript
<div style={{
  marginBottom: '16px',
  flex: member.role === 'Mentor' ? '0 0 100px' : '1 1 auto',
  height: member.role === 'Mentor' ? '100px' : 'auto',
  display: 'flex',
  flexDirection: 'column',
  overflow: 'hidden'
}}>
  {/* 标题部分 */}
  <div style={{
    fontSize: '14px',
    fontWeight: '600',
    color: '#1d1d1f',
    marginBottom: '8px',
    display: 'flex',
    alignItems: 'center',
    flex: '0 0 auto'
  }}>
    <ExperimentOutlined style={{ marginRight: '6px', color: getRoleColor(member.role) }} />
    研究方向
  </div>
  
  {/* 内容部分 */}
  <div style={{
    flex: '1 1 auto',
    overflow: 'hidden'
  }}>
    <Paragraph style={{
      margin: 0,
      fontSize: '13px',
      color: '#86868b',
      lineHeight: '1.5',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      display: '-webkit-box',
      WebkitLineClamp: member.role === 'Mentor' ? 5 : 'none',
      WebkitBoxOrient: 'vertical'
    }}>
      {member.researchDirection}
    </Paragraph>
  </div>
</div>
```

## 技术特性

### 1. 条件样式应用

- 使用三元运算符根据成员角色动态设置样式
- 只对 Mentor 角色应用固定高度，其他角色保持原有样式

### 2. 文本溢出处理

- 使用 CSS `-webkit-box` 和 `-webkit-line-clamp` 实现多行文本省略
- 为 Mentor 角色设置最多显示 5 行文本
- 其他角色不限制行数

### 3. 布局优化

- 使用 Flexbox 布局确保各部分正确对齐
- 研究方向区域使用 `flex: 0 0 100px` 固定高度
- 标题部分使用 `flex: 0 0 auto` 保持固定大小
- 内容部分使用 `flex: 1 1 auto` 填充剩余空间

## 视觉效果

### Mentor 角色成员卡片
- **总高度**: 512px（固定）
- **研究方向区域**: 100px（固定）
- **文本溢出**: 超过 5 行显示省略号
- **布局**: 垂直对齐，内容分布均匀

### 其他角色成员卡片
- **总高度**: 420px（保持原有设置）
- **研究方向区域**: 自适应高度
- **文本溢出**: 无限制
- **布局**: 保持原有布局不变

## 响应式设计

- 保持了原有的响应式网格布局
- 卡片在不同屏幕尺寸下的列数保持不变
- 固定高度确保了卡片在网格中的一致性

## 兼容性

- 使用标准 CSS 属性，兼容现代浏览器
- `-webkit-line-clamp` 属性在主流浏览器中得到良好支持
- Flexbox 布局具有广泛的浏览器兼容性

## 测试验证

- 开发服务器热更新成功
- 无 TypeScript 编译错误
- 无 ESLint 代码质量问题
- 页面可正常访问：http://localhost:5176/team

## 总结

本次修改成功实现了用户要求的功能：

1. ✅ Mentor 角色成员卡片高度固定为 512px
2. ✅ 研究方向区域固定为 100px
3. ✅ 保持卡片内容的垂直对齐和布局美观
4. ✅ 其他角色成员卡片暂时保持原有高度
5. ✅ 适当处理文本溢出（使用省略号）
6. ✅ 保持现有的响应式设计和其他样式特性

修改仅涉及样式相关的代码，没有改变任何功能逻辑或其他布局特性，确保了代码的稳定性和可维护性。

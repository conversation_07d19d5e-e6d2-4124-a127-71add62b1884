# 团队成员页面测试指南

## 📋 概述

本文档提供了团队成员页面的测试指南，包括功能测试、API 集成测试和用户体验测试。

## 🧪 测试环境准备

### 1. 启动后端服务

确保 Strapi 后端服务正在运行：

```bash
cd backend
npm run develop
```

后端服务应该在 `http://localhost:1337` 运行。

### 2. 启动前端服务

```bash
cd frontend
npm run dev
```

前端服务应该在 `http://localhost:5173` 运行。

### 3. 验证环境变量

检查 `frontend/.env` 文件：

```bash
VITE_API_URL=http://localhost:1337
```

## 🔍 功能测试清单

### 基础功能测试

- [ ] **页面加载**
  - 访问 `/team` 页面
  - 页面正常加载，显示标题和描述
  - 无 JavaScript 错误

- [ ] **数据获取**
  - 页面显示加载状态（旋转图标）
  - 成功获取团队成员数据
  - 数据按角色正确分类

- [ ] **角色标签页**
  - 显示所有有数据的角色标签
  - 标签显示正确的成员数量
  - 点击标签可以切换内容

### 数据显示测试

- [ ] **成员卡片**
  - 每个成员显示为独立卡片
  - 卡片包含基本信息（姓名、职称、角色）
  - 研究方向正确显示

- [ ] **头像显示**
  - 有头像的成员正确显示头像
  - 无头像的成员显示默认图标
  - 头像图片加载正常

- [ ] **联系信息**
  - 邮箱地址正确显示
  - 电话号码（如有）正确显示
  - 个人网站链接（如有）可点击

- [ ] **特殊字段显示**
  - 校友显示毕业年份、公司、职位
  - 学生显示入学年份
  - 导师显示完整信息

### 错误处理测试

- [ ] **网络错误**
  - 断开网络连接
  - 页面显示错误提示
  - 提供重新加载按钮

- [ ] **API 错误**
  - 停止后端服务
  - 页面显示适当的错误信息
  - 错误信息用户友好

- [ ] **空数据处理**
  - 某个角色无成员时的处理
  - 所有角色都无成员时的处理

### 响应式测试

- [ ] **桌面端**
  - 1920x1080 分辨率正常显示
  - 1366x768 分辨率正常显示
  - 卡片布局合理

- [ ] **平板端**
  - iPad 尺寸正常显示
  - 卡片自适应调整

- [ ] **移动端**
  - iPhone 尺寸正常显示
  - 卡片单列显示
  - 触摸操作正常

## 🔧 API 集成测试

### 1. 手动 API 测试

使用浏览器或 Postman 测试 API 端点：

```bash
# 获取所有团队成员
GET http://localhost:1337/api/team-members?populate=avatar

# 按角色筛选
GET http://localhost:1337/api/team-members?filters[role][$eq]=PhD&populate=avatar

# 获取单个成员
GET http://localhost:1337/api/team-members/1?populate=avatar
```

### 2. 网络面板检查

在浏览器开发者工具中：

- [ ] 检查 Network 面板
- [ ] 确认 API 请求正确发送
- [ ] 检查响应状态码（200）
- [ ] 验证响应数据格式

### 3. 控制台检查

- [ ] 无 JavaScript 错误
- [ ] 无 API 调用失败警告
- [ ] 数据解析正确

## 🎯 性能测试

### 加载性能

- [ ] **首次加载时间**
  - 页面首次加载 < 3 秒
  - API 数据获取 < 2 秒

- [ ] **图片加载**
  - 头像图片加载优化
  - 无明显的布局跳动

### 内存使用

- [ ] **内存泄漏检查**
  - 多次切换页面无内存增长
  - 组件正确卸载

## 🐛 常见问题排查

### 1. 页面一直显示加载状态

**可能原因：**
- 后端服务未启动
- API URL 配置错误
- CORS 配置问题

**排查步骤：**
```bash
# 检查后端服务
curl http://localhost:1337/api/team-members

# 检查环境变量
cat frontend/.env

# 检查浏览器控制台错误
```

### 2. 头像图片无法显示

**可能原因：**
- 图片路径错误
- 图片文件不存在
- 权限问题

**排查步骤：**
```bash
# 检查图片 URL
# 在浏览器中直接访问图片链接

# 检查 Strapi 文件上传配置
```

### 3. 数据显示不完整

**可能原因：**
- API 响应数据结构变化
- 字段映射错误
- 数据类型不匹配

**排查步骤：**
```bash
# 检查 API 响应数据
curl http://localhost:1337/api/team-members/1?populate=avatar

# 对比类型定义
# 检查数据映射逻辑
```

## 📊 测试报告模板

### 测试环境
- 前端版本：
- 后端版本：
- 浏览器：
- 操作系统：

### 测试结果
- [ ] 基础功能：通过/失败
- [ ] 数据显示：通过/失败
- [ ] 错误处理：通过/失败
- [ ] 响应式：通过/失败
- [ ] 性能：通过/失败

### 发现的问题
1. 问题描述
2. 重现步骤
3. 预期结果
4. 实际结果
5. 严重程度

### 建议改进
1. 功能改进建议
2. 性能优化建议
3. 用户体验改进建议

## 🚀 自动化测试

### 单元测试

```bash
# 运行 API 服务测试
cd frontend
npm test src/api/__tests__/teamMember.test.ts
```

### 集成测试

```bash
# 运行组件集成测试
npm test src/pages/Team/__tests__/
```

### E2E 测试

使用 Cypress 或 Playwright 进行端到端测试：

```javascript
// 示例 E2E 测试
describe('团队成员页面', () => {
  it('应该正确显示团队成员', () => {
    cy.visit('/team');
    cy.contains('团队成员');
    cy.get('[data-testid="member-card"]').should('be.visible');
  });
});
```

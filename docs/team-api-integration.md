# 团队成员页面 API 集成文档

## 📋 概述

本文档描述了团队成员页面从使用模拟数据迁移到 Strapi API 的实现细节。

## 🔄 主要变更

### 1. 环境变量配置

创建了环境变量文件来管理 API 配置：

**frontend/.env**
```bash
# API 配置
VITE_API_URL=http://localhost:1337

# 应用配置
VITE_APP_TITLE=课题组网站
VITE_APP_DESCRIPTION=人工智能课题组官方网站

# 开发环境配置
VITE_DEV_MODE=true
```

### 2. TypeScript 类型定义更新

更新了 `frontend/src/types/index.ts` 中的 `TeamMember` 接口：

```typescript
// 课题组成员类型 - 根据 Strapi Content Type 更新
export interface TeamMember {
  id: number;
  name: string;
  title: string;
  role: 'Mentor' | 'Alumni' | 'PhD' | 'Master' | 'Bachelor';
  avatar?: {
    data?: {
      id: number;
      attributes: {
        url: string;
        name: string;
        alternativeText?: string;
      };
    }[];
  };
  researchDirection: string;
  email: string;
  phone?: string;
  website?: string;
  bio?: any; // Strapi blocks 类型
  education?: string;
  enrollmentYear?: number; // 入学年份（学生）
  graduationYear?: number; // 毕业年份（校友）
  company?: string; // 当前公司（校友）
  position?: string; // 当前职位（校友）
  sortOrder?: number; // 排序权重
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

// Strapi API 响应格式
export interface StrapiTeamMemberResponse {
  data: {
    id: number;
    attributes: TeamMember;
  }[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}
```

### 3. API 服务层

创建了 `frontend/src/api/teamMember.ts` 服务类：

```typescript
export class TeamMemberService {
  // 获取团队成员列表
  static async getTeamMembers(params?: {...}): Promise<StrapiTeamMemberResponse>
  
  // 获取单个团队成员详情
  static async getTeamMember(id: number): Promise<{...}>
  
  // 根据角色获取团队成员
  static async getTeamMembersByRole(role: string): Promise<TeamMember[]>
  
  // CRUD 操作方法
  static async createTeamMember(data: Partial<TeamMember>): Promise<{...}>
  static async updateTeamMember(id: number, data: Partial<TeamMember>): Promise<{...}>
  static async deleteTeamMember(id: number): Promise<{...}>
}
```

### 4. 组件更新

**主要变更点：**

1. **状态管理**：
   - 添加了 `loading`、`error` 状态
   - 使用 `useEffect` 进行数据获取

2. **数据获取**：
   - 并行获取所有角色的成员数据
   - 错误处理和重试机制

3. **UI 增强**：
   - 加载状态显示
   - 错误提示和重新加载功能
   - 头像 URL 处理（支持相对路径转换）

4. **数据映射**：
   - 处理 Strapi 的嵌套数据结构
   - 头像数据的正确解析

## 🔧 技术实现细节

### 数据获取流程

```typescript
useEffect(() => {
  const fetchTeamMembers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // 定义角色列表
      const roles = ['Mentor', 'Alumni', 'PhD', 'Master', 'Bachelor'];
      
      // 并行获取所有角色的成员数据
      const memberPromises = roles.map(async (role) => {
        const members = await TeamMemberService.getTeamMembersByRole(role);
        return { role, members };
      });
      
      const results = await Promise.all(memberPromises);
      
      // 组织数据结构
      const organizedMembers = {};
      results.forEach(({ role, members }) => {
        organizedMembers[role] = members;
      });
      
      setTeamMembers(organizedMembers);
    } catch (error) {
      setError('获取团队成员数据失败，请稍后重试');
      message.error('获取团队成员数据失败');
    } finally {
      setLoading(false);
    }
  };

  fetchTeamMembers();
}, []);
```

### 头像处理

```typescript
const getAvatarUrl = (member: TeamMember): string | undefined => {
  if (member.avatar?.data && member.avatar.data.length > 0) {
    const avatarData = member.avatar.data[0];
    const url = avatarData.attributes.url;
    // 如果是相对路径，添加 API 基础 URL
    if (url.startsWith('/')) {
      return `${import.meta.env.VITE_API_URL || 'http://localhost:1337'}${url}`;
    }
    return url;
  }
  return undefined;
};
```

## 🎯 数据结构映射

### Strapi 字段 → 前端显示

| Strapi 字段 | 前端用途 | 说明 |
|------------|---------|------|
| `name` | 姓名显示 | 必填字段 |
| `title` | 职称显示 | 必填字段 |
| `role` | 角色分类 | 枚举值：Mentor/Alumni/PhD/Master/Bachelor |
| `avatar` | 头像显示 | Media 类型，支持多张图片 |
| `researchDirection` | 研究方向 | 必填字段 |
| `email` | 联系方式 | 必填字段 |
| `phone` | 联系方式 | 可选字段 |
| `website` | 个人网站链接 | 可选字段 |
| `bio` | 个人简介 | Blocks 类型，可选 |
| `education` | 教育背景 | 可选字段 |
| `enrollmentYear` | 入学年份 | 仅学生显示 |
| `graduationYear` | 毕业年份 | 仅校友显示 |
| `company` | 当前公司 | 仅校友显示 |
| `position` | 当前职位 | 仅校友显示 |
| `sortOrder` | 排序权重 | 控制显示顺序 |

## 🚀 部署注意事项

1. **环境变量**：确保生产环境中正确设置 `VITE_API_URL`
2. **CORS 配置**：确保 Strapi 后端允许前端域名的跨域请求
3. **图片路径**：确保头像图片的 URL 路径正确
4. **错误处理**：监控 API 调用失败的情况

## 🔍 测试建议

1. **API 连接测试**：验证与 Strapi 后端的连接
2. **数据加载测试**：测试各种网络条件下的数据加载
3. **错误场景测试**：测试 API 失败时的错误处理
4. **响应式测试**：确保在不同设备上的显示效果

## 📝 后续优化

1. **缓存机制**：添加数据缓存以提升性能
2. **分页加载**：对于大量成员数据的分页处理
3. **搜索功能**：添加成员搜索和筛选功能
4. **图片优化**：添加图片懒加载和压缩

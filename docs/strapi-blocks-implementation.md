# Strapi Rich Text (Blocks) 渲染实现

## 概述

本文档记录了在 React 前端中实现 Strapi v5 官方 rich text blocks 渲染功能的完整过程。

## 实施步骤

### 1. 研究和分析 ✅

- 阅读了 Strapi 官方集成指南：https://strapi.io/blog/integrating-strapi-s-new-rich-text-block-editor-with-next-js-a-step-by-step-guide
- 分析了 Strapi v5 blocks 数据结构和渲染要求
- 确定了使用官方 `@strapi/blocks-react-renderer` 包的最佳实践

### 2. 依赖安装 ✅

使用 yarn 成功安装了官方渲染包：
```bash
yarn add @strapi/blocks-react-renderer
```

安装的版本：`@strapi/blocks-react-renderer@1.0.2`

### 3. 代码实现 ✅

#### 3.1 更新导入语句

在 `frontend/src/pages/Team/index.tsx` 中添加了官方包的导入：
```typescript
import { BlocksRenderer, type BlocksContent } from '@strapi/blocks-react-renderer';
```

#### 3.2 替换自定义渲染函数

将原有的自定义 `renderStrapiBlocks` 函数替换为使用官方 `BlocksRenderer` 的新组件：

```typescript
const StrapiBlocksContent: React.FC<{ content: StrapiBlocks | undefined }> = ({ content }) => {
  if (!content || !Array.isArray(content)) {
    return <span style={{ color: '#86868b', fontStyle: 'italic' }}>暂无信息</span>;
  }

  const blocksContent = content as unknown as BlocksContent;

  return (
    <div style={{ 
      fontSize: '15px',
      color: '#1d1d1f',
      lineHeight: '1.6'
    }}>
      <BlocksRenderer 
        content={blocksContent}
        blocks={{
          // 自定义块样式
          paragraph: ({ children }) => <p style={{ margin: '0 0 12px 0', lineHeight: '1.6' }}>{children}</p>,
          heading: ({ children, level }) => { /* 自定义标题样式 */ },
          list: ({ children, format }) => { /* 自定义列表样式 */ },
          'list-item': ({ children }) => <li style={{ margin: '4px 0' }}>{children}</li>,
          quote: ({ children }) => { /* 自定义引用样式 */ },
          code: ({ children }) => { /* 自定义代码块样式 */ }
        }}
        modifiers={{
          // 自定义文本修饰
          bold: ({ children }) => <strong>{children}</strong>,
          italic: ({ children }) => <em>{children}</em>,
          underline: ({ children }) => <u>{children}</u>,
          strikethrough: ({ children }) => <s>{children}</s>,
          code: ({ children }) => { /* 自定义行内代码样式 */ }
        }}
      />
    </div>
  );
};
```

#### 3.3 更新使用位置

在团队成员详情模态框中，将原有的 `renderStrapiBlocks()` 调用替换为新的组件：

```typescript
// 教育背景
<StrapiBlocksContent content={selectedMentor.education} />

// 个人简介
<StrapiBlocksContent content={selectedMentor.bio} />
```

### 4. 测试和验证 ✅

#### 4.1 创建测试组件

创建了 `frontend/src/components/StrapiBlocksRenderer.test.tsx` 测试组件，包含：
- 完整的测试数据结构
- 各种块类型的示例（段落、标题、列表、引用、代码）
- 文本修饰的示例（粗体、斜体、下划线、删除线、行内代码）

#### 4.2 创建测试页面

创建了 `frontend/src/pages/TestBlocks/index.tsx` 测试页面，展示：
- 测试数据的 JSON 结构
- 实际渲染效果
- 支持的功能特性说明
- 使用方法和代码示例

#### 4.3 构建验证

运行 `npm run build` 成功构建，无 TypeScript 错误。

## 技术特性

### 支持的块类型

- **段落 (paragraph)** - 支持富文本格式
- **标题 (heading)** - 支持 1-6 级标题
- **列表 (list)** - 支持有序和无序列表
- **引用 (quote)** - 支持引用块样式
- **代码 (code)** - 支持代码块高亮
- **图片 (image)** - 支持图片展示
- **链接 (link)** - 支持超链接

### 支持的文本修饰

- **粗体 (bold)**
- **斜体 (italic)**
- **下划线 (underline)**
- **删除线 (strikethrough)**
- **行内代码 (code)**

### 自定义样式

- 自定义段落间距和行高
- 自定义标题样式和颜色
- 自定义列表样式和缩进
- 自定义引用块样式和边框
- 自定义代码块背景和字体

## 优势

### 相比自定义实现的优势

1. **官方支持** - 使用 Strapi 官方维护的渲染包，确保兼容性和稳定性
2. **功能完整** - 支持所有 Strapi v5 blocks 类型，无需手动实现
3. **类型安全** - 提供完整的 TypeScript 类型定义
4. **性能优化** - 官方包经过性能优化，渲染效率更高
5. **维护性** - 减少自定义代码，降低维护成本
6. **扩展性** - 支持自定义块和修饰符，灵活性强

### 与 Strapi 最佳实践一致

- 遵循 Strapi v5 官方推荐的集成方式
- 使用官方提供的类型定义
- 支持客户端渲染要求
- 兼容 Strapi blocks 数据结构

## 文件变更

### 修改的文件

- `frontend/src/pages/Team/index.tsx` - 更新 Strapi blocks 渲染实现
- `frontend/src/App.tsx` - 添加测试页面路由
- `frontend/package.json` - 添加 `@strapi/blocks-react-renderer` 依赖

### 新增的文件

- `frontend/src/components/StrapiBlocksRenderer.test.tsx` - 测试组件
- `frontend/src/pages/TestBlocks/index.tsx` - 测试页面
- `docs/strapi-blocks-implementation.md` - 本文档

## 使用方法

### 基本用法

```typescript
import { BlocksRenderer } from '@strapi/blocks-react-renderer';

<BlocksRenderer content={blocksData} />
```

### 自定义样式

```typescript
<BlocksRenderer 
  content={blocksData}
  blocks={{
    paragraph: ({ children }) => <p className="custom-p">{children}</p>,
    heading: ({ children, level }) => <h{level} className="custom-h">{children}</h{level}>
  }}
  modifiers={{
    bold: ({ children }) => <strong className="custom-bold">{children}</strong>
  }}
/>
```

## 测试访问

- 团队页面：http://localhost:5176/team
- 测试页面：http://localhost:5176/test-blocks

## 总结

成功实现了 Strapi v5 官方 rich text blocks 渲染功能，替换了原有的自定义实现。新实现具有更好的兼容性、完整性和维护性，完全符合 Strapi 官方最佳实践。

# 课题组网站功能需求文档

## 项目概述
基于 React + Strapi + Ant Design + PostgreSQL 技术栈开发的实验小组课题组网站。

## 🎯 核心功能模块

### 1. 网站首页
- 课题组简介和研究领域概述
- 最新动态/新闻轮播展示
- 重要研究成果亮点展示
- 课题组负责人介绍
- 快速导航入口

### 2. 课题组成员管理
- **人员分类展示**：导师、博士生、硕士生、本科生、访问学者
- **个人信息**：姓名、照片、研究方向、联系方式、个人简历
- **成员检索功能**：按年级、研究方向筛选
- **已毕业成员**：校友信息和去向统计

### 3. 研究方向
- 主要研究领域介绍
- 技术路线和方法论
- 相关研究背景和意义
- 支持多媒体内容（图片、视频、图表）

### 4. 科研项目
- **在研项目**：项目名称、负责人、资助机构、研究周期
- **已完成项目**：项目成果和产出
- **项目分类**：国家级、省部级、企业合作等
- **项目详情页**：详细介绍、参与人员、进展状态

### 5. 学术成果
- **论文发表**：期刊论文、会议论文分类展示
- **专利成果**：发明专利、实用新型专利
- **获奖荣誉**：学术奖项、竞赛获奖
- **学术报告**：会议报告、邀请报告记录
- **引用统计**：论文影响因子、引用次数等

### 6. 新闻动态
- 课题组新闻发布
- 学术活动通知
- 会议邀请和参会信息
- 重要事件记录
- 新闻分类和标签管理

### 7. 招生信息
- 研究生招生简章
- 本科生实习机会
- 博士后招聘信息
- 申请要求和流程
- 联系方式

### 8. 科研资源
- **实验设备**：主要仪器设备介绍
- **软件工具**：自研工具、开源项目
- **数据集**：公开数据集、实验数据
- **技术文档**：操作手册、技术指南

### 9. 图片库/相册
- 实验室环境展示
- 学术活动照片
- 团队建设活动
- 重要时刻记录
- 图片分类管理

### 10. 联系我们
- 实验室地址和交通指南
- 联系电话和邮箱
- 课题组负责人信息
- 在线地图集成

## 🔧 技术架构功能

### 前端功能（React + Ant Design）
- 响应式设计，支持移动端
- 多语言支持（中英文切换）
- 搜索功能（全站内容检索）
- 分页和筛选功能
- 图片懒加载和优化
- SEO优化
- 环境变量配置管理
- API 数据获取和错误处理

### 后端功能（Strapi v5 + PostgreSQL）
- RESTful API接口
- 用户权限管理（管理员、编辑者、查看者）
- 内容管理系统（CMS）
- 文件上传和管理
- 数据备份和恢复
- API限流和安全防护

## 🔧 环境变量配置

### 前端环境变量 (frontend/.env)
```bash
# API 配置
VITE_API_URL=http://localhost:1337

# 应用配置
VITE_APP_TITLE=课题组网站
VITE_APP_DESCRIPTION=人工智能课题组官方网站

# 开发环境配置
VITE_DEV_MODE=true
```

### 环境变量说明
- `VITE_API_URL`: Strapi 后端 API 的基础 URL
- `VITE_APP_TITLE`: 应用标题
- `VITE_APP_DESCRIPTION`: 应用描述
- `VITE_DEV_MODE`: 开发模式标志

### 管理员后台功能
- 内容编辑和发布管理
- 用户和权限管理
- 文件资源管理
- 网站统计和分析
- 系统配置管理
- 数据导入导出

## 📋 数据模型设计

需要设计的主要数据表：
- **用户表**：管理员、编辑者信息
- **成员表**：课题组成员信息
- **项目表**：科研项目数据
- **论文表**：学术成果记录
- **新闻表**：动态新闻内容
- **资源表**：科研资源信息
- **分类表**：各类内容分类
- **配置表**：网站基础配置

## 🚀 开发优先级

### 第一阶段（核心功能）
1. 搭建基础架构（Strapi + React项目初始化）
2. 用户管理和权限系统
3. 课题组成员管理模块
4. 基础内容管理（新闻、简介）

### 第二阶段（内容扩展）
1. 科研项目管理
2. 学术成果展示
3. 图片库功能
4. 搜索和筛选功能

### 第三阶段（完善优化）
1. 科研资源管理
2. 招生信息模块
3. 多语言支持
4. 性能优化和SEO

## 📁 项目结构
```
课题组网站/
├── frontend/                 # React前端项目
├── backend/                  # Strapi后端项目
├── docs/                     # 项目文档
│   ├── website-requirements.md
│   ├── progress.md
│   ├── api-specs.md
│   ├── schema-design.md
│   └── deployment.md
├── docker-compose.yml        # Docker部署配置
└── README.md                 # 项目说明
```

## 🛠️ 技术栈
- **后端**：Strapi v5+ (Node.js CMS框架)
- **数据库**：PostgreSQL
- **前端**：React 18+ + Vite
- **UI组件库**：Ant Design v5+
- **版本控制**：Git
- **部署**：Docker + Docker Compose 
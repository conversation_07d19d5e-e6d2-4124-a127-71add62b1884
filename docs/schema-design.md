# 数据模型设计文档

## 📋 概述

本文档定义了课题组网站的数据模型设计，基于Strapi CMS框架构建。所有内容类型都遵循RESTful API设计原则，支持CRUD操作。

## 🗄️ 数据库架构

### 技术栈
- **数据库**: PostgreSQL 15
- **ORM**: Knex.js (Strapi内置)
- **文件存储**: 本地存储 (可配置为云存储)

### 系统表
Strapi自动创建的系统表包括：
- `admin_users`: 管理员用户
- `admin_roles`: 管理员角色
- `admin_permissions`: 管理员权限
- `up_users`: 普通用户
- `up_roles`: 用户角色
- `up_permissions`: 用户权限
- `files`: 文件管理
- `strapi_*`: 系统配置表

## 👥 课题组成员 (Team Member)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `name` | Text | ✅ | 姓名 |
| `title` | Text | ✅ | 职称/头衔 |
| `role` | Enumeration | ✅ | 角色 (导师/博士生/硕士生/本科生/访问学者) |
| `avatar` | Media | ❌ | 头像图片 |
| `researchDirection` | Text | ✅ | 研究方向 |
| `email` | Email | ✅ | 邮箱地址 |
| `phone` | Text | ❌ | 电话号码 |
| `bio` | Rich Text | ❌ | 个人简介 |
| `education` | Text | ❌ | 教育背景 |
| `publications` | JSON | ❌ | 发表论文列表 |
| `awards` | JSON | ❌ | 获奖情况 |
| `socialLinks` | JSON | ❌ | 社交媒体链接 |
| `isActive` | Boolean | ✅ | 是否在职 |
| `joinDate` | Date | ❌ | 加入时间 |
| `graduateDate` | Date | ❌ | 毕业时间 |

### 枚举值定义
```javascript
// 角色枚举
role: {
  type: 'enumeration',
  enum: ['导师', '博士生', '硕士生', '本科生', '访问学者']
}
```

### 关联关系
- 与科研项目: 多对多关系
- 与学术成果: 多对多关系

## 🔬 研究方向 (Research Direction)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `title` | Text | ✅ | 研究方向标题 |
| `description` | Rich Text | ✅ | 详细描述 |
| `keywords` | JSON | ✅ | 关键词列表 |
| `image` | Media | ❌ | 相关图片 |
| `applications` | JSON | ❌ | 应用领域 |
| `achievements` | JSON | ❌ | 主要成果 |
| `technologies` | JSON | ❌ | 相关技术 |
| `isActive` | Boolean | ✅ | 是否活跃 |
| `order` | Integer | ❌ | 显示顺序 |

### JSON字段示例
```javascript
// keywords字段
keywords: ["深度学习", "计算机视觉", "机器学习"]

// applications字段
applications: [
  "智能监控系统",
  "自动驾驶技术",
  "医疗影像诊断"
]

// achievements字段
achievements: [
  "在ImageNet竞赛中取得优异成绩",
  "发表多篇CVPR、ICCV顶级会议论文"
]
```

## 📊 科研项目 (Research Project)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `title` | Text | ✅ | 项目标题 |
| `description` | Rich Text | ✅ | 项目描述 |
| `leader` | Text | ✅ | 项目负责人 |
| `members` | JSON | ✅ | 项目成员列表 |
| `funding` | Text | ✅ | 资助机构 |
| `budget` | Decimal | ❌ | 项目经费(万元) |
| `startDate` | Date | ✅ | 开始时间 |
| `endDate` | Date | ❌ | 结束时间 |
| `status` | Enumeration | ✅ | 项目状态 |
| `category` | Enumeration | ✅ | 项目类别 |
| `progress` | Integer | ❌ | 项目进度(0-100) |
| `keywords` | JSON | ❌ | 关键词 |
| `achievements` | JSON | ❌ | 主要成果 |
| `cover` | Media | ❌ | 项目封面图 |

### 枚举值定义
```javascript
// 项目状态
status: {
  type: 'enumeration',
  enum: ['进行中', '已完成', '计划中']
}

// 项目类别
category: {
  type: 'enumeration',
  enum: ['国家级', '省部级', '企业合作', '其他']
}
```

### 关联关系
- 与课题组成员: 多对多关系
- 与学术成果: 一对多关系

## 📚 学术成果 (Academic Achievement)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `title` | Text | ✅ | 成果标题 |
| `type` | Enumeration | ✅ | 成果类型 |
| `authors` | JSON | ✅ | 作者列表 |
| `journal` | Text | ❌ | 期刊/会议名称 |
| `publicationDate` | Date | ✅ | 发表时间 |
| `doi` | Text | ❌ | DOI号 |
| `impactFactor` | Decimal | ❌ | 影响因子 |
| `citations` | Integer | ❌ | 引用次数 |
| `keywords` | JSON | ❌ | 关键词 |
| `abstract` | Rich Text | ❌ | 摘要 |
| `description` | Rich Text | ❌ | 详细描述 |
| `file` | Media | ❌ | 相关文件 |
| `isPublished` | Boolean | ✅ | 是否已发表 |

### 枚举值定义
```javascript
// 成果类型
type: {
  type: 'enumeration',
  enum: ['论文', '专利', '获奖', '报告']
}
```

### 关联关系
- 与课题组成员: 多对多关系
- 与科研项目: 多对一关系

## 📰 新闻动态 (News)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `title` | Text | ✅ | 新闻标题 |
| `summary` | Text | ✅ | 新闻摘要 |
| `content` | Rich Text | ✅ | 新闻内容 |
| `category` | Enumeration | ✅ | 新闻类别 |
| `tags` | JSON | ❌ | 标签列表 |
| `author` | Text | ✅ | 作者 |
| `cover` | Media | ❌ | 封面图片 |
| `publishedAt` | DateTime | ✅ | 发布时间 |
| `views` | Integer | ❌ | 浏览次数 |
| `isPublished` | Boolean | ✅ | 是否发布 |
| `featured` | Boolean | ❌ | 是否置顶 |

### 枚举值定义
```javascript
// 新闻类别
category: {
  type: 'enumeration',
  enum: ['项目获批', '学术成果', '学术交流', '会议参会', '专利授权', '其他']
}
```

## 🏢 机构信息 (Organization)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `name` | Text | ✅ | 机构名称 |
| `type` | Enumeration | ✅ | 机构类型 |
| `description` | Rich Text | ❌ | 机构描述 |
| `logo` | Media | ❌ | 机构Logo |
| `website` | Text | ❌ | 官方网站 |
| `address` | Text | ❌ | 地址 |
| `contact` | JSON | ❌ | 联系方式 |
| `isActive` | Boolean | ✅ | 是否活跃 |

### 枚举值定义
```javascript
// 机构类型
type: {
  type: 'enumeration',
  enum: ['高校', '研究所', '企业', '政府机构', '其他']
}
```

## 📁 文件管理 (File)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `name` | Text | ✅ | 文件名 |
| `alternativeText` | Text | ❌ | 替代文本 |
| `caption` | Text | ❌ | 说明文字 |
| `width` | Integer | ❌ | 图片宽度 |
| `height` | Integer | ❌ | 图片高度 |
| `formats` | JSON | ❌ | 格式信息 |
| `hash` | Text | ✅ | 文件哈希 |
| `ext` | Text | ✅ | 文件扩展名 |
| `mime` | Text | ✅ | MIME类型 |
| `size` | Decimal | ✅ | 文件大小 |
| `url` | Text | ✅ | 文件URL |
| `previewUrl` | Text | ❌ | 预览URL |
| `provider` | Text | ✅ | 存储提供商 |
| `provider_metadata` | JSON | ❌ | 提供商元数据 |

## 🔍 搜索索引 (Search Index)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `query` | Text | ✅ | 搜索关键词 |
| `type` | Text | ✅ | 内容类型 |
| `contentId` | Integer | ✅ | 内容ID |
| `title` | Text | ✅ | 标题 |
| `description` | Text | ❌ | 描述 |
| `tags` | JSON | ❌ | 标签 |
| `createdAt` | DateTime | ✅ | 创建时间 |

## 📊 统计信息 (Statistics)

### 字段设计
| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `type` | Text | ✅ | 统计类型 |
| `data` | JSON | ✅ | 统计数据 |
| `date` | Date | ✅ | 统计日期 |
| `period` | Text | ❌ | 统计周期 |

## 🔗 关联关系设计

### 一对多关系
- 科研项目 → 学术成果 (一个项目可以有多个成果)
- 研究方向 → 科研项目 (一个方向可以有多个项目)

### 多对多关系
- 课题组成员 ↔ 科研项目 (成员可以参与多个项目，项目可以有多个成员)
- 课题组成员 ↔ 学术成果 (成员可以有多个成果，成果可以有多个作者)

### 一对多关系 (可选)
- 机构信息 → 科研项目 (机构可以资助多个项目)

## 📝 数据验证规则

### 课题组成员
```javascript
// 邮箱格式验证
email: {
  type: 'email',
  required: true,
  unique: true
}

// 角色枚举验证
role: {
  type: 'enumeration',
  enum: ['导师', '博士生', '硕士生', '本科生', '访问学者'],
  required: true
}
```

### 科研项目
```javascript
// 进度范围验证
progress: {
  type: 'integer',
  min: 0,
  max: 100
}

// 日期验证
startDate: {
  type: 'date',
  required: true
}

endDate: {
  type: 'date',
  required: false,
  // 结束日期必须晚于开始日期
  custom: (value, { startDate }) => {
    if (value && startDate && new Date(value) <= new Date(startDate)) {
      throw new Error('结束日期必须晚于开始日期');
    }
  }
}
```

### 学术成果
```javascript
// 影响因子验证
impactFactor: {
  type: 'decimal',
  min: 0,
  max: 100
}

// 引用次数验证
citations: {
  type: 'integer',
  min: 0
}
```

## 🔐 权限设计

### 角色定义
1. **超级管理员**: 所有权限
2. **内容管理员**: 内容管理权限
3. **编辑者**: 内容编辑权限
4. **查看者**: 只读权限

### 权限矩阵
| 操作 | 超级管理员 | 内容管理员 | 编辑者 | 查看者 |
|------|------------|------------|--------|--------|
| 创建内容 | ✅ | ✅ | ✅ | ❌ |
| 编辑内容 | ✅ | ✅ | ✅ | ❌ |
| 删除内容 | ✅ | ✅ | ❌ | ❌ |
| 发布内容 | ✅ | ✅ | ❌ | ❌ |
| 查看内容 | ✅ | ✅ | ✅ | ✅ |
| 用户管理 | ✅ | ❌ | ❌ | ❌ |
| 系统设置 | ✅ | ❌ | ❌ | ❌ |

## 📈 数据迁移策略

### 版本控制
- 使用Strapi内置的迁移系统
- 每个内容类型变更创建迁移文件
- 支持向前和向后兼容

### 数据备份
- 定期备份PostgreSQL数据库
- 备份上传的文件
- 备份配置文件

### 数据导入导出
- 支持JSON格式的数据导入导出
- 支持CSV格式的批量导入
- 提供数据清理和验证工具

## 🔧 性能优化

### 索引设计
```sql
-- 课题组成员表索引
CREATE INDEX idx_team_members_role ON team_members(role);
CREATE INDEX idx_team_members_active ON team_members(is_active);

-- 科研项目表索引
CREATE INDEX idx_projects_status ON research_projects(status);
CREATE INDEX idx_projects_category ON research_projects(category);
CREATE INDEX idx_projects_dates ON research_projects(start_date, end_date);

-- 学术成果表索引
CREATE INDEX idx_achievements_type ON academic_achievements(type);
CREATE INDEX idx_achievements_date ON academic_achievements(publication_date);

-- 新闻表索引
CREATE INDEX idx_news_category ON news(category);
CREATE INDEX idx_news_published ON news(published_at);
CREATE INDEX idx_news_featured ON news(featured);
```

### 查询优化
- 使用关联查询减少数据库请求
- 实现分页查询避免大量数据加载
- 使用缓存减少重复查询
- 优化图片和文件存储

### 缓存策略
- 使用Redis缓存热点数据
- 实现API响应缓存
- 缓存静态资源
- 定期清理过期缓存 
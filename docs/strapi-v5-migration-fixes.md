# Strapi v5 数据结构修复文档

## 🔍 问题分析

在团队成员页面集成过程中发现的主要问题：

### 1. 实际 API 响应结构与预期不符

**实际 Strapi v5 响应结构：**
```json
{
  "data": [
    {
      "id": 2,
      "documentId": "go4x4cze2hncuejy8qy4ng4h",
      "name": "李清泉",
      "title": "中国工程院院士，二级教授，博士生导师",
      "role": "Mentor",
      "avatar": [
        {
          "id": 1,
          "documentId": "e49uvjsgbfwrwzxwlsk7e18x",
          "name": "lqq.jpg",
          "url": "/uploads/lqq_84c34cf030.jpg",
          "formats": {
            "thumbnail": { "url": "/uploads/thumbnail_lqq_84c34cf030.jpg" },
            "small": { "url": "/uploads/small_lqq_84c34cf030.jpg" },
            "medium": { "url": "/uploads/medium_lqq_84c34cf030.jpg" },
            "large": { "url": "/uploads/large_lqq_84c34cf030.jpg" }
          }
        }
      ]
    }
  ]
}
```

**之前预期的 Strapi v4 结构：**
```json
{
  "data": [
    {
      "id": 2,
      "attributes": {
        "name": "李清泉",
        "avatar": {
          "data": [
            {
              "id": 1,
              "attributes": {
                "url": "/uploads/lqq_84c34cf030.jpg"
              }
            }
          ]
        }
      }
    }
  ]
}
```

### 2. 关键差异

1. **扁平化结构**：Strapi v5 移除了 `attributes` 包装层
2. **头像结构变化**：从 `{data: [{attributes: {...}}]}` 变为直接数组 `[{...}]`
3. **新增字段**：添加了 `documentId` 字段
4. **图片格式**：提供了多种尺寸的图片格式

## 🔧 修复内容

### 1. 更新 TypeScript 接口

**文件：** `frontend/src/types/index.ts`

```typescript
// 更新前
export interface TeamMember {
  avatar?: {
    data?: {
      id: number;
      attributes: {
        url: string;
        name: string;
        alternativeText?: string;
      };
    }[];
  };
}

export interface StrapiTeamMemberResponse {
  data: {
    id: number;
    attributes: TeamMember;
  }[];
}

// 更新后
export interface TeamMember {
  documentId?: string; // Strapi v5 新增字段
  avatar?: {
    id: number;
    documentId?: string;
    name: string;
    alternativeText?: string;
    url: string;
    formats?: {
      thumbnail?: { url: string; width: number; height: number; };
      small?: { url: string; width: number; height: number; };
      medium?: { url: string; width: number; height: number; };
      large?: { url: string; width: number; height: number; };
    };
  }[];
}

export interface StrapiTeamMemberResponse {
  data: TeamMember[]; // 直接数组，无 attributes 包装
}
```

### 2. 修复 API 服务数据映射

**文件：** `frontend/src/api/teamMember.ts`

```typescript
// 更新前
return response.data.map(item => ({
  id: item.id,
  ...item.attributes
}));

// 更新后
return response.data; // Strapi v5 已经是扁平化结构
```

### 3. 修复头像 URL 处理

**文件：** `frontend/src/pages/Team/index.tsx`

```typescript
// 更新前
const getAvatarUrl = (member: TeamMember): string | undefined => {
  if (member.avatar?.data && member.avatar.data.length > 0) {
    const avatarData = member.avatar.data[0];
    const url = avatarData.attributes.url;
    // ...
  }
};

// 更新后
const getAvatarUrl = (member: TeamMember): string | undefined => {
  if (member.avatar && member.avatar.length > 0) {
    const avatarData = member.avatar[0];
    // 优先使用 small 格式，如果没有则使用原图
    const url = avatarData.formats?.small?.url || avatarData.url;
    // ...
  }
};
```

## 🎯 修复效果

### 1. 头像显示
- ✅ 正确解析 Strapi v5 的头像数组结构
- ✅ 优先使用 small 格式图片以提升加载性能
- ✅ 正确构建完整的图片 URL

### 2. 成员信息显示
- ✅ 正确映射所有成员字段
- ✅ 处理 Strapi v5 的扁平化数据结构
- ✅ 支持新的 documentId 字段

### 3. 数据获取
- ✅ API 调用正确返回数据
- ✅ 错误处理机制正常工作
- ✅ 加载状态正确显示

## 🧪 测试验证

### 1. API 响应测试
```bash
# 测试 API 端点
curl "http://localhost:1337/api/team-members?populate=avatar"

# 按角色筛选
curl "http://localhost:1337/api/team-members?filters[role][\$eq]=Mentor&populate=avatar"
```

### 2. 前端功能测试
- 访问 `http://localhost:5175/team`
- 检查成员头像是否正确显示
- 验证成员信息是否完整
- 测试不同角色的成员显示

### 3. 浏览器开发者工具检查
- Network 面板：确认 API 请求成功
- Console 面板：无 JavaScript 错误
- Elements 面板：头像图片正确加载

## 📋 Strapi v4 vs v5 主要差异总结

| 方面 | Strapi v4 | Strapi v5 |
|------|-----------|-----------|
| 响应结构 | `{data: [{id, attributes: {...}}]}` | `{data: [{id, ...}]}` |
| 关系字段 | `{data: [{attributes: {...}}]}` | `[{...}]` |
| 文档标识 | 仅 `id` | `id` + `documentId` |
| 媒体字段 | 嵌套 attributes | 直接对象结构 |
| 图片格式 | 需要手动处理 | 自动生成多种尺寸 |

## 🚀 后续优化建议

1. **图片优化**：利用 Strapi v5 的多格式图片，根据显示尺寸选择合适格式
2. **缓存策略**：添加图片和数据缓存以提升性能
3. **错误处理**：增强网络错误和数据格式错误的处理
4. **类型安全**：进一步完善 TypeScript 类型定义

## 📝 注意事项

1. **向后兼容**：如果需要支持 Strapi v4，需要添加版本检测逻辑
2. **环境配置**：确保 `.env` 文件中的 API URL 正确
3. **CORS 设置**：确保 Strapi 后端允许前端域名访问
4. **图片路径**：注意相对路径和绝对路径的处理
